<script>
    var label_please_wait = '<?php echo e(get_label('please_wait', 'Please wait...')); ?>';
    var label_please_select_records_to_delete =
        '<?php echo e(get_label('please_select_records_to_delete', 'Please select records to delete.')); ?>';
    var label_something_went_wrong = '<?php echo e(get_label('something_went_wrong', 'Something went wrong.')); ?>';
    var label_please_correct_errors = '<?php echo e(get_label('please_correct_errors', 'Please correct errors.')); ?>';
    var label_project_removed_from_favorite_successfully =
        '<?php echo e(get_label('project_removed_from_favorite_successfully', 'Project removed from favorite successfully.')); ?>';
    var label_project_marked_as_favorite_successfully =
        '<?php echo e(get_label('project_marked_as_favorite_successfully', 'Project marked as favorite successfully.')); ?>';
    var label_yes = '<?php echo e(get_label('yes', 'Yes')); ?>';
    var label_upload = '<?php echo e(get_label('upload', 'Upload')); ?>';
    var decimal_points = <?php echo e(intval($general_settings['decimal_points_in_currency'] ?? '2')); ?>;
    var label_update = '<?php echo e(get_label('update', 'Update')); ?>';
    var label_delete = '<?php echo e(get_label('delete', 'Delete')); ?>';
    var label_view = '<?php echo e(get_label('view', 'View')); ?>';
    var label_not_assigned = '<?php echo e(get_label('not_assigned', 'Not assigned')); ?>';
    var label_delete_selected = '<?php echo e(get_label('delete_selected', 'Delete selected')); ?>';
    var label_search = '<?php echo e(get_label('search', 'Search')); ?>';
    var label_create = '<?php echo e(get_label('create', 'Create')); ?>';
    var label_min_0 = '<?php echo e(get_label('value_must_be_greater_then_0', 'Value must be greater than 0')); ?>';
    var label_max_100 = '<?php echo e(get_label('not_greater_then_100', 'Not greater than 100')); ?>';
    var label_set_as_default_view = '<?= get_label('set_as_default_view', 'Set as Default View') ?>';
    var label_users_associated_with_project =
        '<?= get_label('users_associated_with_project', 'Users associated with project') ?>';
    var label_update_task = '<?= get_label('update_task', 'Update Task') ?>';
    var label_quick_view = '<?= get_label('quick_view', 'Quick View') ?>';
    var label_project = '<?= get_label('project', 'Project') ?>';
    var label_task = '<?= get_label('task', 'Task') ?>';
    var label_projects = '<?= get_label('projects', 'Projects') ?>';
    var label_tasks = '<?= get_label('tasks', 'Tasks') ?>';
    var label_clear_filters = '<?= get_label('clear_filters', 'Clear Filters') ?>';
    var label_set_as_default_view = '<?= get_label('set_as_default_view', 'Set as Default View') ?>';
    var label_default_view = '<?= get_label('default_view', 'Default View') ?>';
    var label_save_column_visibility = '<?= get_label('save_column_visibility', 'Save Column Visibility') ?>';
    var preview_not_available_label = '<?php echo e(get_label('preview_not_available', 'Preview not available')); ?>';
    var label_filter_status = "<?= get_label('filter_by_status', 'Filter by status') ?>";
    var label_select_user = "<?= get_label('select_user', 'Select user') ?>";
    var label_select_client = "<?= get_label('select_client', 'Select client') ?>";
    var label_select_project = "<?= get_label('select_project', 'Select project') ?>";
    var label_select_date_range = "<?= get_label('select_date_range', 'Select date range') ?>";
    var label_select_user = "<?= get_label('select_user', 'Select user') ?>";
    var label_select_client = "<?= get_label('select_client', 'Select client') ?>";
    var label_select_priority = "<?= get_label('select_priority', 'Select priority') ?>";
    var label_create_plan = "<?php echo e(get_label('create_plan_button', 'Create Plan')); ?>";
    var label_update_plan = "<?php echo e(get_label('update_plan_button', 'Update Plan')); ?>";
    var label_status_not_changed_warning =
        "<?php echo e(get_label('project_status_unchanged_no_update_performed', 'Project status unchanged. No Update performed.')); ?>";
    var label_income = "<?php echo e(get_label('income', 'Income')); ?>";
    var label_expenses = "<?php echo e(get_label('expenses', 'Expenses')); ?>";
    var label_income_vs_expenses = "<?php echo e(get_label('income_vs_expenses', 'Income vs Expenses')); ?>";
    var labelTotalIncome = "<?php echo e(get_label('total_income', 'Total Income')); ?>";
    var labelTotalExpenses = "<?php echo e(get_label('total_expenses', 'Total Expenses')); ?>";
    var labelNetResult = "<?php echo e(get_label('net_result', 'Net Result')); ?>";
    var label_total = "<?php echo e(get_label('total', 'Total')); ?>";
    var label_all_time = "<?php echo e(get_label('all_time', 'All Time')); ?>";
    var label_no_task_list = "<?php echo e(get_label('no_task_list', 'No Task List')); ?>";
    var label_searching = "<?php echo e(get_label('searching', 'Searching...')); ?>";
    var label_delete_selected = "<?php echo e(get_label('delete_selected', 'Delete selected')); ?>";
    var label_select_source = "<?php echo e(get_label('select_lead_source', 'Select Lead Source')); ?>";
    var label_select_stage = "<?php echo e(get_label('select_lead_stage', 'Select Lead Stage')); ?>";
    var label_select_candidates = "<?php echo e(get_label('select_candidates', 'Select Candidates')); ?>";
    var label_select_candidates_statuses = "<?php echo e(get_label('select_candidates_statuses', 'Select Candidates Statuses')); ?>";
    var label_select_search_candidates = "<?php echo e(get_label('search_candidate', 'Search Candidates')); ?>";
    var label_select_search_interviewer = "<?php echo e(get_label('search_interviewer', 'Search Interviewer')); ?>";

</script>
<?php /**PATH C:\Users\<USER>\Downloads\a\resources\views/labels.blade.php ENDPATH**/ ?>