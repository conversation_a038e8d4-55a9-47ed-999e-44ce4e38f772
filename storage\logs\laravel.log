[2025-07-01 11:04:13] local.INFO: Migration completed  
[2025-07-01 11:04:13] local.INFO: Starting manual queries execution  
[2025-07-01 11:04:13] local.INFO: Package Data: {
    "archives": ".\/archives.json",
    "files": ".\/files.json",
    "folders": ".\/folders.json",
    "manual_queries": true,
    "query_path": "query.sql",
    "version": "1.2.3"
}  
[2025-07-01 11:04:13] local.INFO: Starting manual queries execution  
[2025-07-01 11:04:13] local.INFO: Query content: INSERT INTO settings (`variable`, `value`, `created_at`, `updated_at`) VALUES (
  'frontend_features_settings',
  '{
    "features": [
      {
        "title": "Projects",
        "description": "Manage all your company projects from one dashboard.",
        "icon": "frontend/features/project.svg"
      },
      {
        "title": "Tasks",
        "description": "Create and assign tasks to your team effectively.",
        "icon": "frontend/features/task.svg"
      },
      {
        "title": "Users",
        "description": "Handle team roles, permissions, and access easily.",
        "icon": "frontend/features/user.svg"
      },
      {
        "title": "Clients",
        "description": "Manage client records and communication.",
        "icon": "frontend/features/client.svg"
      },
      {
        "title": "Chat",
        "description": "Instant messaging between team members.",
        "icon": "frontend/features/chat.svg"
      },
      {
        "title": "Meetings",
        "description": "Schedule and manage virtual meetings.",
        "icon": "frontend/features/meetings.svg"
      },
      {
        "title": "Payslips",
        "description": "Generate and distribute employee payslips.",
        "icon": "frontend/features/payslips.svg"
      },
      {
        "title": "Finance",
        "description": "Track expenses and handle company finances.",
        "icon": "frontend/features/expenses.svg"
      },
      {
        "title": "Language",
        "description": "Use the app in multiple languages.",
        "icon": "frontend/features/language.svg"
      },
      {
        "title": "Security",
        "description": "Ensure your data is protected and secure.",
        "icon": "frontend/features/lock-closed.svg"
      },
      {
        "title": "Subscription",
        "description": "Manage your current plan and upgrades.",
        "icon": "frontend/features/subscription.svg"
      },
      {
        "title": "Collaboration",
        "description": "Real-time collaboration for team productivity.",
        "icon": "frontend/features/collab.svg"
      },
      {
        "title": "Activity Log",
        "description": "Track recent user activities and actions.",
        "icon": "frontend/features/activity_log.svg"
      },
      {
        "title": "Currency",
        "description": "Support for Indian Rupee billing.",
        "icon": "frontend/features/currency-rupee.svg"
      },
      {
        "title": "Status",
        "description": "Monitor current progress and statuses.",
        "icon": "frontend/features/status.svg"
      },
      {
        "title": "Clock",
        "description": "Track time spent on tasks.",
        "icon": "frontend/features/clock.svg"
      },
      {
        "title": "Notes",
        "description": "Write down important notes for your work.",
        "icon": "frontend/features/notes.svg"
      },
      {
        "title": "Todos",
        "description": "Track daily to-do items and reminders.",
        "icon": "frontend/features/todos.svg"
      }
    ]
  }',
  NOW(),
  NOW()
);

  
[2025-07-01 11:10:17] local.INFO: Migration completed  
[2025-07-01 11:10:17] local.INFO: Starting manual queries execution  
[2025-07-01 11:10:17] local.INFO: Package Data: {
    "archives": ".\/archives.json",
    "files": ".\/files.json",
    "folders": ".\/folders.json",
    "manual_queries": true,
    "query_path": "query.sql",
    "version": "1.2.3"
}  
[2025-07-01 11:10:17] local.INFO: Starting manual queries execution  
[2025-07-01 11:10:17] local.INFO: Query content: INSERT INTO settings (`variable`, `value`, `created_at`, `updated_at`) VALUES (
  'frontend_features_settings',
  '{
    "features": [
      {
        "title": "Projects",
        "description": "Manage all your company projects from one dashboard.",
        "icon": "frontend/features/project.svg"
      },
      {
        "title": "Tasks",
        "description": "Create and assign tasks to your team effectively.",
        "icon": "frontend/features/task.svg"
      },
      {
        "title": "Users",
        "description": "Handle team roles, permissions, and access easily.",
        "icon": "frontend/features/user.svg"
      },
      {
        "title": "Clients",
        "description": "Manage client records and communication.",
        "icon": "frontend/features/client.svg"
      },
      {
        "title": "Chat",
        "description": "Instant messaging between team members.",
        "icon": "frontend/features/chat.svg"
      },
      {
        "title": "Meetings",
        "description": "Schedule and manage virtual meetings.",
        "icon": "frontend/features/meetings.svg"
      },
      {
        "title": "Payslips",
        "description": "Generate and distribute employee payslips.",
        "icon": "frontend/features/payslips.svg"
      },
      {
        "title": "Finance",
        "description": "Track expenses and handle company finances.",
        "icon": "frontend/features/expenses.svg"
      },
      {
        "title": "Language",
        "description": "Use the app in multiple languages.",
        "icon": "frontend/features/language.svg"
      },
      {
        "title": "Security",
        "description": "Ensure your data is protected and secure.",
        "icon": "frontend/features/lock-closed.svg"
      },
      {
        "title": "Subscription",
        "description": "Manage your current plan and upgrades.",
        "icon": "frontend/features/subscription.svg"
      },
      {
        "title": "Collaboration",
        "description": "Real-time collaboration for team productivity.",
        "icon": "frontend/features/collab.svg"
      },
      {
        "title": "Activity Log",
        "description": "Track recent user activities and actions.",
        "icon": "frontend/features/activity_log.svg"
      },
      {
        "title": "Currency",
        "description": "Support for Indian Rupee billing.",
        "icon": "frontend/features/currency-rupee.svg"
      },
      {
        "title": "Status",
        "description": "Monitor current progress and statuses.",
        "icon": "frontend/features/status.svg"
      },
      {
        "title": "Clock",
        "description": "Track time spent on tasks.",
        "icon": "frontend/features/clock.svg"
      },
      {
        "title": "Notes",
        "description": "Write down important notes for your work.",
        "icon": "frontend/features/notes.svg"
      },
      {
        "title": "Todos",
        "description": "Track daily to-do items and reminders.",
        "icon": "frontend/features/todos.svg"
      }
    ]
  }',
  NOW(),
  NOW()
);

  
[2025-07-01 12:04:29] local.ERROR: Undefined variable $get_current_version {"view":{"view":"/home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/components/footer.blade.php","data":[]},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $get_current_version at /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/components/footer.blade.php:8)
[stacktrace]
#0 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#1 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#3 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#4 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#5 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#6 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#7 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesComponents.php(103): Illuminate\\View\\View->render()
#8 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/layout.blade.php(100): Illuminate\\View\\Factory->renderComponent()
#9 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#10 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#12 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#13 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#14 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#15 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#16 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/settings/general_settings.blade.php(351): Illuminate\\View\\View->render()
#17 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#18 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#20 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#21 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#22 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#23 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#24 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#25 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#26 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#27 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#28 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#29 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#30 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CustomRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CustomRoleMiddleware->handle()
#32 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CheckSuperAdmin.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckSuperAdmin->handle()
#34 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/Language.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle()
#36 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#38 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#40 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#42 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#44 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#45 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#47 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#49 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#51 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#52 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#53 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#54 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#55 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#56 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/SecureInputHandling.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SecureInputHandling->handle()
#58 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#60 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#61 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#63 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#64 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#66 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#68 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#70 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#71 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#72 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#74 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#75 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#76 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#77 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $get_current_version at /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/a91461ce5f6e2b9946f3c54f86594dd1.php:9)
[stacktrace]
#0 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/a91461ce5f6e2b9946f3c54f86594dd1.php(9): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#3 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#8 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#9 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesComponents.php(103): Illuminate\\View\\View->render()
#10 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/8e74d9e5fb6623800ff30db176ac6f29.php(129): Illuminate\\View\\Factory->renderComponent()
#11 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#12 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#14 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#15 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#16 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#17 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#18 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/94951f8f5df9485d84ddba4e777adf5c.php(437): Illuminate\\View\\View->render()
#19 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#20 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#22 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#23 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#24 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#25 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#26 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#27 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#28 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#29 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#30 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#31 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#32 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CustomRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CustomRoleMiddleware->handle()
#34 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CheckSuperAdmin.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckSuperAdmin->handle()
#36 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/Language.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle()
#38 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#40 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#42 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#44 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#46 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#47 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#49 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#51 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#53 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#54 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#55 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#56 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#57 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#58 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/SecureInputHandling.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SecureInputHandling->handle()
#60 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#62 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#63 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#65 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#66 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#68 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#70 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#71 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#72 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#74 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#75 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#76 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#77 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#78 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#79 {main}
"} 
[2025-07-01 12:14:41] local.ERROR: Undefined variable ${"id":12,"version":"1.2.1","created_at":"2025-04-10T09:20:33.000000Z","updated_at":"2025-04-10T09:20:33.000000Z"} {"view":{"view":"/home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/components/footer.blade.php","data":[]},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable ${\"id\":12,\"version\":\"1.2.1\",\"created_at\":\"2025-04-10T09:20:33.000000Z\",\"updated_at\":\"2025-04-10T09:20:33.000000Z\"} at /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/app_helpers.php:570)
[stacktrace]
#0 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/app_helpers.php(570): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/components/footer.blade.php(9): get_current_version()
#3 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#4 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#6 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#7 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#8 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#9 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#10 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesComponents.php(103): Illuminate\\View\\View->render()
#11 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/layout.blade.php(100): Illuminate\\View\\Factory->renderComponent()
#12 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#13 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#15 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#16 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#17 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#18 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#19 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/resources/views/settings/general_settings.blade.php(351): Illuminate\\View\\View->render()
#20 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#21 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#22 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#23 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#24 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#25 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#26 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#27 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#28 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#29 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#30 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#31 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#32 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#33 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CustomRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CustomRoleMiddleware->handle()
#35 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CheckSuperAdmin.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckSuperAdmin->handle()
#37 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/Language.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle()
#39 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#41 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#43 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#45 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#47 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#48 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#50 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#52 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#54 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#55 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#56 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#57 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#58 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#59 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/SecureInputHandling.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SecureInputHandling->handle()
#61 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#63 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#64 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#66 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#67 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#69 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#71 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#73 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#74 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#75 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#77 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#78 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#79 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#80 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable ${\"id\":12,\"version\":\"1.2.1\",\"created_at\":\"2025-04-10T09:20:33.000000Z\",\"updated_at\":\"2025-04-10T09:20:33.000000Z\"} at /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/app_helpers.php:570)
[stacktrace]
#0 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/app_helpers.php(570): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/a91461ce5f6e2b9946f3c54f86594dd1.php(10): get_current_version()
#3 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#4 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#6 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#7 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#8 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#9 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#10 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesComponents.php(103): Illuminate\\View\\View->render()
#11 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/8e74d9e5fb6623800ff30db176ac6f29.php(129): Illuminate\\View\\Factory->renderComponent()
#12 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#13 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#15 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#16 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#17 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#18 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#19 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/storage/framework/views/94951f8f5df9485d84ddba4e777adf5c.php(437): Illuminate\\View\\View->render()
#20 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#21 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#22 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#23 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#24 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get()
#25 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#26 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#27 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#28 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#29 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#30 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#31 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#32 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#33 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CustomRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CustomRoleMiddleware->handle()
#35 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/CheckSuperAdmin.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckSuperAdmin->handle()
#37 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/Language.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle()
#39 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#41 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#43 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#45 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#47 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#48 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#50 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#52 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#54 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#55 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#56 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#57 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#58 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#59 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/app/Http/Middleware/SecureInputHandling.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SecureInputHandling->handle()
#61 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#63 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#64 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#66 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#67 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#68 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#69 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#70 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#71 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#72 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#73 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#74 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#75 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#76 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#77 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#78 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#79 /home/<USER>/Desktop/Taskify saas fresh install Test Final/TEst/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#80 {main}
"} 
[2025-07-31 12:24:17] local.ERROR: Maximum execution time of 3600 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 3600 seconds exceeded at C:\\Users\\<USER>\\Downloads\\a\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
[2025-07-31 12:24:52] local.ERROR: Maximum execution time of 3600 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 3600 seconds exceeded at C:\\Users\\<USER>\\Downloads\\a\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
