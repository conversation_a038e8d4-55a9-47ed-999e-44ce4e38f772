@charset "UTF-8";
/* -------------------------------------------------------------------------- */
/*                                 Theme                                      */
/* -------------------------------------------------------------------------- */
/* prettier-ignore */
/* -------------------------------------------------------------------------- */
/*                                  Utilities                                 */
/* -------------------------------------------------------------------------- */
:root {
  --gohub-blue: #045bc1;
  --gohub-indigo: #1C16AF;
  --gohub-purple: #6f42c1;
  --gohub-pink: #D032D0;
  --gohub-red: #d0021b;
  --gohub-orange: #fd7e14;
  --gohub-yellow: #f37f29;
  --gohub-green: #7ed321;
  --gohub-teal: #20c997;
  --gohub-cyan: #00d6ff;
  --gohub-white: #fff;
  --gohub-gray: #7F7F7F;
  --gohub-gray-dark: #5E5D61;
  --gohub-gray-black: #000;
  --gohub-gray-100: #F5F2FC;
  --gohub-gray-200: #f2f2f2;
  --gohub-gray-300: #E7E4EE;
  --gohub-gray-400: #bebebe;
  --gohub-gray-500: #949494;
  --gohub-gray-600: #7F7F7F;
  --gohub-gray-700: #717075;
  --gohub-gray-800: #5E5D61;
  --gohub-gray-900: #403F42;
  --gohub-gray-1000: #212240;
  --gohub-gray-1100: #1c1c1c;
  --gohub-gray-white: #fff;
  --gohub-primary: #D32F2F;
  --gohub-secondary: #B71C1C;
  --gohub-success: #7ed321;
  --gohub-info: #1C16AF;
  --gohub-warning: #f37f29;
  --gohub-danger: #d0021b;
  --gohub-light: #F5F2FC;
  --gohub-dark: #1c1c1c;
  --gohub-primary-rgb: 211, 47, 47;
  --gohub-secondary-rgb: 183, 28, 28;
  --gohub-success-rgb: 126, 211, 33;
  --gohub-info-rgb: 28, 22, 175;
  --gohub-warning-rgb: 243, 127, 41;
  --gohub-danger-rgb: 208, 2, 27;
  --gohub-light-rgb: 245, 242, 252;
  --gohub-dark-rgb: 28, 28, 28;
  --gohub-white-rgb: 255, 255, 255;
  --gohub-black-rgb: 0, 0, 0;
  --gohub-body-color-rgb: 113, 112, 117;
  --gohub-body-bg-rgb: 255, 255, 255;
  --gohub-font-sans-serif: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --gohub-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --gohub-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --gohub-body-font-family: Poppins, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  --gohub-body-font-size: 1rem;
  --gohub-body-font-weight: 400;
  --gohub-body-line-height: 1.45;
  --gohub-body-color: #717075;
  --gohub-body-bg: #fff;
}

:root, :root.light, :root .light {
  --gohub-facebook: #3c5a99;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-facebook-hover-color: #30487a;
  --gohub-google-plus: #dd4b39;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-google-plus-hover-color: #b13c2e;
  --gohub-twitter: #1da1f2;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-twitter-hover-color: #1781c2;
  --gohub-linkedin: #0077b5;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-linkedin-hover-color: #005f91;
  --gohub-youtube: #ff0001;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-youtube-hover-color: #cc0001;
  --gohub-github: #333333;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-github-hover-color: #292929;
  --gohub-black: #000;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-black-hover-color: black;
  --gohub-100: #F5F2FC;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-100-hover-color: #f7f5fd;
  --gohub-200: #f2f2f2;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-200-hover-color: whitesmoke;
  --gohub-300: #E7E4EE;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-300-hover-color: #ece9f1;
  --gohub-400: #bebebe;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-400-hover-color: #cbcbcb;
  --gohub-500: #949494;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-500-hover-color: #767676;
  --gohub-600: #7F7F7F;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-600-hover-color: #666666;
  --gohub-700: #717075;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-700-hover-color: #5a5a5e;
  --gohub-800: #5E5D61;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-800-hover-color: #4b4a4e;
  --gohub-900: #403F42;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-900-hover-color: #333235;
  --gohub-1000: #212240;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-1000-hover-color: #1a1b33;
  --gohub-1100: #1c1c1c;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-1100-hover-color: #161616;
  --gohub-white: #fff;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-white-hover-color: white;
  --gohub-primary: #D32F2F;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-primary-hover-color: #B71C1C;
  --gohub-secondary: #B71C1C;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-secondary-hover-color: #8D1515;
  --gohub-success: #7ed321;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-success-hover-color: #98dc4d;
  --gohub-info: #1C16AF;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-info-hover-color: #16128c;
  --gohub-warning: #f37f29;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-warning-hover-color: #c26621;
  --gohub-danger: #d0021b;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-danger-hover-color: #a60216;
  --gohub-light: #F5F2FC;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-light-hover-color: #f7f5fd;
  --gohub-dark: #1c1c1c;
  /* ------------------------------ Colored Link ------------------------------ */
  --gohub-colored-link-dark-hover-color: #161616;
  --gohub-soft-primary: #eee8f8;
  --gohub-soft-secondary: #f9e6f9;
  --gohub-soft-success: #f0fae4;
  --gohub-soft-info: #e4e3f5;
  --gohub-soft-warning: #fef0e5;
  --gohub-soft-danger: #f9e1e4;
  --gohub-soft-light: #fefdff;
  --gohub-soft-dark: #e4e4e4;
  /* -------------------------------- Accordion ------------------------------- */
  --gohub-accordion-button-active-color: var(--gohub-1000);
  --gohub-accordion-button-active-bg: $white;
  /* ------------------------------ Reveal Button ----------------------------- */
  --gohub-btn-reveal-color: dark;
  --gohub-btn-reveal-bg: #e6e6e6;
  --gohub-btn-reveal-border-color: #dfdfdf;
  /* ------------------------------ Falcon Button ----------------------------- */
  --gohub-btn-falcon-background: var(--gohub-white);
  --gohub-btn-disabled-color: #ab64c0;
  --gohub-btn-falcon-box-shadow: 0 0 0 1px rgba(43, 45, 80, 0.1), 0 2px 5px 0 rgba(43, 45, 80, 0.08), 0 1px 1.5px 0 rgba(0, 0, 0, 0.07), 0 1px 2px 0 rgba(0, 0, 0, 0.08);
  --gohub-btn-falcon-hover-box-shadow: 0 0 0 1px rgba(43, 45, 80, 0.1), 0 2px 5px 0 rgba(43, 45, 80, 0.1), 0 3px 9px 0 rgba(43, 45, 80, 0.08), 0 1px 1.5px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.08);
  --gohub-btn-falcon-default-color: #5E5D61;
  --gohub-btn-falcon-default-hover-color: #4e4d51;
  --gohub-btn-falcon-default-active-background: #e6e6e6;
  --gohub-btn-falcon-default-active-border: #4e4d51;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-primary-color: var(--gohub-primary);
  --gohub-btn-falcon-primary-hover-color: #B71C1C;
  --gohub-btn-falcon-primary-active-background: #FFEBEE;
  --gohub-btn-falcon-primary-active-color: #B71C1C;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-primary-background: #FFEBEE;
  --gohub-alert-primary-border-color: #FFCDD2;
  --gohub-alert-primary-color: #B71C1C;
  --gohub-alert-primary-link-color: #D32F2F;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-primary-background: #FFEBEE;
  --gohub-list-group-item-primary-color: #B71C1C;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-secondary-color: var(--gohub-secondary);
  --gohub-btn-falcon-secondary-hover-color: #8D1515;
  --gohub-btn-falcon-secondary-active-background: #FFCDD2;
  --gohub-btn-falcon-secondary-active-color: #8D1515;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-secondary-background: #FFCDD2;
  --gohub-alert-secondary-border-color: #FFAB91;
  --gohub-alert-secondary-color: #8D1515;
  --gohub-alert-secondary-link-color: #B71C1C;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-secondary-background: #FFCDD2;
  --gohub-list-group-item-secondary-color: #8D1515;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-success-color: var(--gohub-success);
  --gohub-btn-falcon-success-hover-color: #69af1b;
  --gohub-btn-falcon-success-active-background: #e5f6d3;
  --gohub-btn-falcon-success-active-color: #69af1b;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-success-background: #e5f6d3;
  --gohub-alert-success-border-color: #d8f2bc;
  --gohub-alert-success-color: #4c7f14;
  --gohub-alert-success-link-color: #65a91a;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-success-background: #e5f6d3;
  --gohub-list-group-item-success-color: #4c7f14;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-info-color: var(--gohub-info);
  --gohub-btn-falcon-info-hover-color: #171291;
  --gohub-btn-falcon-info-active-background: #d2d0ef;
  --gohub-btn-falcon-info-active-color: #171291;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-info-background: #d2d0ef;
  --gohub-alert-info-border-color: #bbb9e7;
  --gohub-alert-info-color: #110d69;
  --gohub-alert-info-link-color: #16128c;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-info-background: #d2d0ef;
  --gohub-list-group-item-info-color: #110d69;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-warning-color: var(--gohub-warning);
  --gohub-btn-falcon-warning-hover-color: #ca6922;
  --gohub-btn-falcon-warning-active-background: #fde5d4;
  --gohub-btn-falcon-warning-active-color: #ca6922;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-warning-background: #fde5d4;
  --gohub-alert-warning-border-color: #fbd9bf;
  --gohub-alert-warning-color: #924c19;
  --gohub-alert-warning-link-color: #c26621;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-warning-background: #fde5d4;
  --gohub-list-group-item-warning-color: #924c19;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-danger-color: var(--gohub-danger);
  --gohub-btn-falcon-danger-hover-color: #ad0216;
  --gohub-btn-falcon-danger-active-background: #f6ccd1;
  --gohub-btn-falcon-danger-active-color: #ad0216;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-danger-background: #f6ccd1;
  --gohub-alert-danger-border-color: #f1b3bb;
  --gohub-alert-danger-color: #7d0110;
  --gohub-alert-danger-link-color: #a60216;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-danger-background: #f6ccd1;
  --gohub-list-group-item-danger-color: #7d0110;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-light-color: var(--gohub-light);
  --gohub-btn-falcon-light-hover-color: #cbc9d1;
  --gohub-btn-falcon-light-active-background: #fdfcfe;
  --gohub-btn-falcon-light-active-color: #cbc9d1;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-light-background: #fdfcfe;
  --gohub-alert-light-border-color: #fcfbfe;
  --gohub-alert-light-color: #939197;
  --gohub-alert-light-link-color: #c4c2ca;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-light-background: #fdfcfe;
  --gohub-list-group-item-light-color: #939197;
  /* ------------------------------ Falcon button ----------------------------- */
  --gohub-btn-falcon-dark-color: var(--gohub-dark);
  --gohub-btn-falcon-dark-hover-color: #171717;
  --gohub-btn-falcon-dark-active-background: #d2d2d2;
  --gohub-btn-falcon-dark-active-color: #171717;
  /* ---------------------------------- Alert --------------------------------- */
  --gohub-alert-dark-background: #d2d2d2;
  --gohub-alert-dark-border-color: #bbbbbb;
  --gohub-alert-dark-color: #111111;
  --gohub-alert-dark-link-color: #161616;
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-item-dark-background: #d2d2d2;
  --gohub-list-group-item-dark-color: #111111;
  --gohub-transparent-50: rgba(255, 255, 255, 0.5);
  /* ------------------------------- List Group ------------------------------- */
  --gohub-list-group-bg: #fff;
  --gohub-list-group-action-hover-color: #717075;
  --gohub-list-group-hover-bg: #F5F2FC;
  --gohub-body-bg: #fff;
  --gohub-body-color: #717075;
  --gohub-headings-color: #000;
  --gohub-link-color: #6f42c1;
  --gohub-link-hover-color: #59359a;
  --gohub-border-color: #E7E4EE;
  /* --------------------------------- Shadow --------------------------------- */
  --gohub-box-shadow: 0 7px 14px 0 rgba(65, 69, 88, 0.1), 0 3px 6px 0 rgba(0, 0, 0, 0.07);
  --gohub-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --gohub-box-shadow-lg: 0 1rem 4rem rgba(0, 0, 0, 0.175);
  --gohub-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --gohub-component-active-color: ;
  --gohub-component-active-bg: #6f42c1;
  --gohub-text-muted: #949494;
  --gohub-blockquote-footer-color: #7F7F7F;
  --gohub-hr-color: var(--gohub-border-color);
  --gohub-hr-opacity: 0.25;
  --gohub-mark-bg: #fcf8e3;
  --gohub-input-btn-focus-color: rgba(211, 47, 47, 0.25);
  --gohub-btn-link-color: #D32F2F;
  --gohub-btn-link-hover-color: #B71C1C;
  --gohub-btn-disabled-color: #7F7F7F;
  --gohub-heading-color: #000;
  /* ---------------------------------- Form ---------------------------------- */
  --gohub-input-bg: #fff;
  --gohub-input-color: #403F42;
  --gohub-input-border-color: #E7E4EE;
  --gohub-input-focus-border-color: #b7a1e0;
  --gohub-input-focus-color: var(--gohub-input-color);
  --gohub-input-placeholder-color: #bebebe;
  --gohub-input-plaintext-color: #717075;
  --gohub-form-check-label-color: ;
  --gohub-form-check-input-bg: transparent;
  --gohub-form-check-input-border: 1px solid var(--gohub-form-check-input-border-color);
  --gohub-form-check-input-border-color: #bebebe;
  --gohub-form-check-input-checked-color: #fff;
  --gohub-form-check-input-checked-bg-color: #6f42c1;
  --gohub-form-check-input-checked-border-color: #6f42c1;
  --gohub-form-check-input-indeterminate-color: #fff;
  --gohub-form-check-input-indeterminate-bg-color: #6f42c1;
  --gohub-form-check-input-indeterminate-border-color: #6f42c1;
  --gohub-form-switch-color:rgba(0, 0, 0, .25);
  --gohub-form-switch-focus-color: #b7a1e0;
  --gohub-form-switch-checked-color: #fff;
  --gohub-input-group-addon-color: var(--gohub-input-border-color);
  --gohub-input-group-addon-bg: #f2f2f2;
  --gohub-input-group-addon-border-color: var(--gohub-input-border-color);
  --gohub-form-select-color: var(--gohub-input-color);
  --gohub-form-select-disabled-color: #7F7F7F;
  --gohub-form-select-bg: var(--gohub-input-bg);
  --gohub-form-select-disabled-bg: #f2f2f2;
  --gohub-form-select-indicator-color: #5E5D61;
  --gohub-form-select-focus-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  --gohub-form-select-border-color: var(--gohub-input-border-color);
  --gohub-form-select-focus-border-color: #b7a1e0;
  --gohub-form-range-track-bg: #E7E4EE;
  --gohub-form-range-track-box-shadow: var(--gohub-box-shadow-inset);
  --gohub-form-range-thumb-bg: #6f42c1;
  --gohub-form-range-thumb-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  --gohub-form-range-thumb-focus-box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  --gohub-form-range-thumb-active-bg: #d5c8ed;
  --gohub-form-range-thumb-disabled-bg: #949494;
  --gohub-form-file-focus-border-color: #b7a1e0;
  --gohub-form-file-focus-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  --gohub-form-file-disabled-bg: var(--gohub-200);
  --gohub-form-file-disabled-border-color: ;
  --gohub-form-file-color: var(--gohub-input-color);
  --gohub-form-file-bg: var(--gohub-input-bg);
  --gohub-form-file-border-color: var(--gohub-input-border-color);
  --gohub-form-file-box-shadow: null;
  --gohub-form-file-button-bg: var(--gohub-200);
  --gohub-form-feedback-valid-color: #7ed321;
  --gohub-form-feedback-invalid-color: #d0021b;
  --gohub-form-feedback-icon-valid-color: var(--gohub-form-feedback-valid-color);
  --gohub-form-feedback-icon-invalid-color: var(--gohub-form-feedback-invalid-color);
  /* ----------------------------------- Nav ---------------------------------- */
  --gohub-nav-link-color: null;
  --gohub-nav-link-hover-color: null;
  --gohub-nav-link-disabled-color: #7F7F7F;
  --gohub-nav-tabs-border-color: #E7E4EE;
  --gohub-nav-tabs-link-hover-border-color: #f2f2f2 #f2f2f2 #E7E4EE;
  --gohub-nav-tabs-link-active-color: #717075;
  --gohub-nav-tabs-link-active-bg: #fff;
  --gohub-nav-tabs-link-active-border-color: #E7E4EE #E7E4EE #fff;
  --gohub-bg-navbar-glass: rgba(242, 242, 242, 0.96);
  --gohub-nav-pills-link-active-color: #fff;
  --gohub-nav-pills-link-active-bg: #D32F2F;
  /* --------------------------------- Navbar --------------------------------- */
  --gohub-navbar-dark-color: rgba(255, 255, 255, 0.7);
  --gohub-navbar-dark-hover-color: rgba(255, 255, 255, 0.8);
  --gohub-navbar-dark-active-color: #fff;
  --gohub-navbar-dark-disabled-color: rgba(255, 255, 255, 0.25);
  --gohub-navbar-dark-toggler-border-color: rgba(255, 255, 255, 0.1);
  --gohub-navbar-light-color: rgba(0, 0, 0, 0.55);
  --gohub-navbar-light-hover-color: rgba(0, 0, 0, 0.9);
  --gohub-navbar-light-active-color: #000;
  --gohub-navbar-light-disabled-color: rgba(0, 0, 0, 0.25);
  --gohub-navbar-light-toggler-border-color: rgba(0, 0, 0, 0.1);
  --gohub-navbar-light-brand-color: #D32F2F;
  --gohub-navbar-dark-brand-color: #D32F2F;
  /* -------------------------------------------------------------------------- */
  /*                               Navbar Vertical                              */
  /* -------------------------------------------------------------------------- */
  /* --------------------------------- Default -------------------------------- */
  --gohub-navbar-vertical-collapsed-hover-shadow-color: rgba(0, 0, 0, 0.2);
  --gohub-navbar-vertical-bg-color: var(--gohub-bg-navbar-glass);
  --gohub-navbar-vertical-link-color: #717075;
  --gohub-navbar-vertical-link-hover-color: #212240;
  --gohub-navbar-vertical-link-active-color: #D32F2F;
  --gohub-navbar-vertical-link-disable-color: #bebebe;
  --gohub-navbar-vertical-hr-color: rgba(0, 0, 0, 0.08);
  --gohub-navbar-vertical-scrollbar-color: rgba(127, 127, 127, 0.3);
  /* ----------------------------- Navbar Inverted ---------------------------- */
  /* --------------------------- End Navbar Vertical -------------------------- */
  /* -------------------------------- Dropdown -------------------------------- */
  --gohub-dropdown-bg: #fff;
  --gohub-dropdown-color: #E7E4EE;
  --gohub-dropdown-border-color: var(--gohub-border-color);
  --gohub-dropdown-box-shadow: var(--gohub-box-shadow);
  --gohub-dropdown-link-color: #403F42;
  --gohub-dropdown-link-hover-color: #333335;
  --gohub-dropdown-link-hover-bg: #F5F2FC;
  --gohub-dropdown-link-active-color: #fff;
  --gohub-dropdown-link-active-bg: #6f42c1;
  --gohub-dropdown-link-disabled-color: #7F7F7F;
  --gohub-dropdown-header-color: #7F7F7F;
  --gohub-dropdown-dark-color: #E7E4EE;
  --gohub-dropdown-dark-bg: #5E5D61;
  --gohub-dropdown-dark-border-color: var(--gohub-dropdown-border-color);
  --gohub-dropdown-dark-divider-bg: var(--gohub-dropdown-border-color);
  --gohub-dropdown-dark-box-shadow: null;
  --gohub-dropdown-dark-link-color: #E7E4EE;
  --gohub-dropdown-dark-link-hover-color: #fff;
  --gohub-dropdown-dark-link-hover-bg: rgba(255, 255, 255, 0.15);
  --gohub-dropdown-dark-link-active-color: var(--gohub-dropdown-link-active-color);
  --gohub-dropdown-dark-link-active-bg: var(--gohub-dropdown-link-active-bg);
  --gohub-dropdown-dark-link-disabled-color: #949494;
  --gohub-dropdown-dark-header-color: #949494;
  /* ------------------------------- Pagination ------------------------------- */
  --gohub-pagination-color: #000;
  --gohub-pagination-bg: #fff;
  --gohub-pagination-border-color: #f2f2f2;
  --gohub-pagination-hover-color: #fff;
  --gohub-pagination-hover-bg: #6f42c1;
  --gohub-pagination-hover-border-color: #6f42c1;
  --gohub-pagination-active-color: #fff;
  --gohub-pagination-active-bg: #6f42c1;
  --gohub-pagination-active-border-color: #6f42c1;
  --gohub-pagination-disabled-bg: #fff;
  /* ---------------------------------- Card ---------------------------------- */
  --gohub-card-border-color: rgba(0, 0, 0, 0.125);
  --gohub-card-cap-bg: rgba(0, 0, 0, 0.03);
  --gohub-card-cap-color: null;
  --gohub-card-color: null;
  --gohub-card-bg: #fff;
  /* --------------------------------- Tooltip -------------------------------- */
  --gohub-tooltip-color: #fff;
  --gohub-tooltip-bg: #000;
  --gohub-tooltip-arrow-color: #000;
  /* --------------------------------- Popover -------------------------------- */
  --gohub-popover-bg: #fff;
  --gohub-popover-border-color: rgba(0, 0, 0, 0.2);
  --gohub-popover-box-shadow: var(--gohub-box-shadow);
  --gohub-popover-header-bg: #f0f0f0;
  --gohub-popover-header-border-bottom-color: #d6d6d6;
  --gohub-popover-header-color: var(--gohub-headings-color);
  --gohub-popover-body-color: #717075;
  --gohub-popover-arrow-color: #fff;
  --gohub-popover-arrow-outer-color: rgba(0, 0, 0, 0.25);
  /* ---------------------------------- Toast --------------------------------- */
  --gohub-toast-color: ;
  --gohub-toast-background-color: rgba(255, 255, 255, 0.85);
  --gohub-toast-border-color: rgba(0, 0, 0, .1);
  --gohub-toast-box-shadow: var(--gohub-box-shadow);
  --gohub-toast-header-color: #7F7F7F;
  --gohub-toast-header-background-color: rgba(255, 255, 255, 0.85);
  --gohub-toast-header-border-color: rgba(0, 0, 0, .05);
  /* ---------------------------------- Badge --------------------------------- */
  --gohub-badge-color: #fff;
  /* ---------------------------------- Modal --------------------------------- */
  --gohub-modal-content-bg: #fff;
  /* ---------------------------------- Table --------------------------------- */
  --gohub-table-border-color: #f2f2f2;
  --gohub-table-primary-bg: #FFEBEE;
  --gohub-table-primary-color: #5E5D61;
  --gohub-table-primary-hover-bg: #FFCDD2;
  --gohub-table-primary-hover-color: #5E5D61;
  --gohub-table-primary-striped-bg: #FFD7DA;
  --gohub-table-primary-striped-color: #5E5D61;
  --gohub-table-primary-active-bg: #FFCDD2;
  --gohub-table-primary-active-color: #5E5D61;
  --gohub-table-primary-border-color: #FFCDD2;
  --gohub-table-secondary-bg: #f6d6f6;
  --gohub-table-secondary-color: #5E5D61;
  --gohub-table-secondary-hover-bg: #ebcdeb;
  --gohub-table-secondary-hover-color: #5E5D61;
  --gohub-table-secondary-striped-bg: #eed0ef;
  --gohub-table-secondary-striped-color: #5E5D61;
  --gohub-table-secondary-active-bg: #e7cae7;
  --gohub-table-secondary-active-color: #5E5D61;
  --gohub-table-secondary-border-color: #e7cae7;
  --gohub-table-success-bg: #e5f6d3;
  --gohub-table-success-color: #5E5D61;
  --gohub-table-success-hover-bg: #dbebca;
  --gohub-table-success-hover-color: #5E5D61;
  --gohub-table-success-striped-bg: #deeecd;
  --gohub-table-success-striped-color: #5E5D61;
  --gohub-table-success-active-bg: #d8e7c8;
  --gohub-table-success-active-color: #5E5D61;
  --gohub-table-success-border-color: #d8e7c8;
  --gohub-table-info-bg: #d2d0ef;
  --gohub-table-info-color: #5E5D61;
  --gohub-table-info-hover-bg: #c9c7e4;
  --gohub-table-info-hover-color: #5E5D61;
  --gohub-table-info-striped-bg: #cccae8;
  --gohub-table-info-striped-color: #5E5D61;
  --gohub-table-info-active-bg: #c6c5e1;
  --gohub-table-info-active-color: #5E5D61;
  --gohub-table-info-border-color: #c6c5e1;
  --gohub-table-warning-bg: #fde5d4;
  --gohub-table-warning-color: #5E5D61;
  --gohub-table-warning-hover-bg: #f1dbcb;
  --gohub-table-warning-hover-color: #5E5D61;
  --gohub-table-warning-striped-bg: #f5dece;
  --gohub-table-warning-striped-color: #5E5D61;
  --gohub-table-warning-active-bg: #edd7c9;
  --gohub-table-warning-active-color: #5E5D61;
  --gohub-table-warning-border-color: #edd7c9;
  --gohub-table-danger-bg: #f6ccd1;
  --gohub-table-danger-color: #5E5D61;
  --gohub-table-danger-hover-bg: #ebc4c9;
  --gohub-table-danger-hover-color: #5E5D61;
  --gohub-table-danger-striped-bg: #eec6cb;
  --gohub-table-danger-striped-color: #5E5D61;
  --gohub-table-danger-active-bg: #e7c1c6;
  --gohub-table-danger-active-color: #5E5D61;
  --gohub-table-danger-border-color: #e7c1c6;
  --gohub-table-light-bg: #F5F2FC;
  --gohub-table-light-color: #5E5D61;
  --gohub-table-light-hover-bg: #eae7f0;
  --gohub-table-light-hover-color: #5E5D61;
  --gohub-table-light-striped-bg: #edebf4;
  --gohub-table-light-striped-color: #5E5D61;
  --gohub-table-light-active-bg: #e6e3ed;
  --gohub-table-light-active-color: #5E5D61;
  --gohub-table-light-border-color: #e6e3ed;
  --gohub-table-dark-bg: #1c1c1c;
  --gohub-table-dark-color: #fff;
  --gohub-table-dark-hover-bg: #2d2d2d;
  --gohub-table-dark-hover-color: #fff;
  --gohub-table-dark-striped-bg: #272727;
  --gohub-table-dark-striped-color: #fff;
  --gohub-table-dark-active-bg: #333333;
  --gohub-table-dark-active-color: #fff;
  --gohub-table-dark-border-color: #333333;
  /* --------------------------------- Avarar --------------------------------- */
  --gohub-avatar-status-border-color: #fff;
  --gohub-avatar-name-bg: #403F42;
  --gohub-avatar-button-bg: #bebebe;
  --gohub-avatar-button-hover-bg: #949494;
  /* ------------------------------ Notification ------------------------------ */
  --gohub-notification-bg: #fff;
  --gohub-notification-title-bg: #F5F2FC;
  --gohub-notification-unread-bg: #f2f2f2;
  --gohub-notification-unread-hover-bg: #e5e5e5;
  --gohub-notification-indicator-border-color: #f2f2f2;
  /* --------------------------------- Kanban --------------------------------- */
  --gohub-kanban-bg: #F5F2FC;
  --gohub-kanban-header-bg: var(--gohub-kanban-bg);
  --gohub-kanban-item-bg: #fff;
  --gohub-kanban-item-color: #403F42;
  --gohub-kanban-btn-add-hover-bg: #f2f2f2;
  --gohub-kanban-draggable-source-dragging-bg: #f2f2f2;
  --gohub-kanban-bg-attachment-bg: #E7E4EE;
  --gohub-kanban-footer-color: #7F7F7F;
  --gohub-kanban-nav-link-card-details-color: #717075;
  --gohub-kanban-nav-link-card-details-hover-bg: #f2f2f2;
  /* --------------------------- Gradient Background -------------------------- */
  --gohub-bg-shape-bg: #4695ff;
  --gohub-bg-shape-bg-ltd: linear-gradient(-45deg, #4695ff, #1970e2);
  --gohub-bg-shape-bg-dtl: linear-gradient(-45deg, #1970e2, #4695ff);
  --gohub-line-chart-gradient: linear-gradient(-45deg, #014ba7, #0183d0);
  --gohub-card-gradient: linear-gradient(-45deg, #1970e2, #4695ff);
  --gohub-progress-gradient: linear-gradient(-45deg, #1970e2, #4695ff);
  --gohub-bg-circle-shape: none;
  --gohub-bg-circle-shape-bg: #4695ff;
  --gohub-modal-shape-header: linear-gradient(-45deg, #1970e2, #4695ff);
  --gohub-modal-shape-header-bg: #4494ff;
  /* ------------------------------ Full Calendar ----------------------------- */
  --fc-button-bg-color: #403F42;
  --fc-button-border-color: #403F42;
  --fc-button-hover-bg-color: #1c1c1c;
  --fc-button-hover-border-color: #1c1c1c;
  --fc-button-active-bg-color: #1c1c1c;
  --fc-button-active-border-color: #1c1c1c;
  --fc-button-list-day-cushion: #fff;
  /* -------------------------------- Flatpickr ------------------------------- */
  --gohub-flatpickr-calendar-bg: #fff;
  /* -------------------------------- Flatpickr ------------------------------- */
  --gohub-leaflet-bar-bg: #fff;
  --gohub-leaflet-popup-content-wrapper-bg: #fff;
  /* --------------------------------- Choices -------------------------------- */
  --gohub-choices-item-selectable-highlighted-bg: #fff;
  --gohub-choices-item-has-no-choices-bg: #fff;
  /* -------------------------------- Thumbnail ------------------------------- */
  --gohub-thumbnail-bg: #fff;
  /* ---------------------------------- Chat ---------------------------------- */
  --gohub-chat-contact-bg: var(--gohub-card-bg);
  /* --------------------------------- Tinymce -------------------------------- */
  --gohub-tinymce-bg: #fff;
  /* --------------------------------- Swiper --------------------------------- */
  --gohub-swiper-nav-bg: rgba(255, 255, 255, 0.8);
  /* --------------------------------- Rater --------------------------------- */
  --gohub-star-rating-bg-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFdElEQVR4nO2aXWxURRTH/2dut58Imho1SgStuit+vBo0BkgkRjDog+VBiJEXqJhKK21x+7EdUu5u2W1Uqi8Qo4YQY6gQDQlRn9RENGoMD0a7RCzGRCJGBLRF9mOOD+waKHe7e2f23mt0f4/zceY/Z2fmzJm7QI0aNWr8j6EgBu3sHG9oXHC2gxQvJ/CUhbpUPD5wMggtwv8hmZrmn3mHmF8G4XEm6s5R/kinlPP91xKAA7YN2A8CeHRW8eLmrOjwWwsQgAMYqs+xnLGls3O8wW89vjqgp1/eDWC1YyXhxqYFvz/ppx7AZwcIsnrmbMDUK6X0V5NfA/VJuRDgdWWa3Tmds1b5IqiAf97OUReAunLNCOx4RniFL/eALimvrs+KnwDMq6yHWJq0Bz/3VFRxJD8GabgY4iqcPABWvd6puRzPV4CUsnEmK6YA3OCiG1tKRRIJecwrXUU8XwHTGbEe7iYPAJQXYqsXeq4YyEvjUkoxkxXfAbhDo/uFOrYWxeMDv1Rb16V4ugJmctYa6E0eABpyItdZTT1OeLwFDEMa02YpZeWHpwaeOaBncOQhMJYamrnmvMdJUtXOgI0bd4daW0+25S0rAoV7iLiHgWqkuDkChjik9ibl8EmAuAo2/8G1A7ZKea24IMJkcQRAmJkiBIQBtAGwqiluNgScU4w0EU2CkWZSk8ycnleP76WUf2nadCYajbfmrfwDxIWJgiMAhQG06k7AQxSAEwAmiTnNRJMEMWkxpePx/lNzrRpHB7wwOLJcMR+Cm9vbv5cZAK81h1S3lFLNrnQ8BBVjF/4bkweAZgDPnc+IJ5wqS0QBDnsoKBAYdLtTubMDCF95qiYAhMUfOpY7FSqlOgCc9lSRjzDR86MjsS+d6hwdMBaX3zCrlQDOeKrMBwiIpnYMvVSqvuRNMBWXXwPiYQB/eKLMBxgY3mnHRudqM+dVOGkPfsEKjwCYrqoyHyAgnrKHRipoV57CveAwgCZjZT7AwFjKHuqr5Npc8VW4r3/7ShAdAuD7xwuXjCftoa5KcwZXuUDv4MgqYn4XQEhLmscw8+5UPPaMm4TJVTqc2jF0GERrAeRcq/MceqOlnje7zRa10uHege3tBHobgXxdvhIm7DuRjjw9MbE277av1gRS9vAEwE8BqGpurgVhf0ud2qAz+YvdDegb3L4BTK+b2DCBgffO/np9+549m7K6NoweMD795KOj9y9bcRsB95rY0SRTp+qW7drV/aeJEeM9TMzvm9rQg48mEv2/mVoxP8RYLDa2oYWor4oVYwuEu6qgQwOOtLfvN36DrEIY4yXmNrRobGv79hZTI0YOKPwCEVMRurBFxqvPyAGLw+lbEWBuoBCwAwhB7f/i+GS8/YwcwBzY/i8S7AoILgIUMY8EZluAzZegIY2Fc0gbbQdc9DwHFgGKCFZGP4K2AwoxuFGz+xkCehFSNxHzKoCO6uowjQRl/7dXCs0YnCXmVwWH7Evu8T9LKT+Yzoj1BNggLHRl0fAc0ncAiyVuHl8INKEI0aQdOz67rvDRcm9394sToabpLhBHAVxVkV02c4D2e0DfwMi+Cv76CoCPKEU9Y4nYZ5Xa7pHyOpEVwwA2oXzKfmHqWKRF90FEewVUkAMcZ/C2lB076PadbkzKUwCejUblK3kSO0FYM0fzhkXh9M0AptyMUcQkDJY6AE8D2NIcUktS9vABk7+0JBJyMhmPPcYKywEu9cGWQ3nrnO4Y2g5gYN+sogyIU5mQakvasXEpZUbX9mxSidjHzSG+jxjrAPx4eS29afIwor0FWkJq9HzOOsvMq5n5B0vx2OioPKFrrxyFg/ItKeXBmazoAGMFQEea6/MlP3zWqFGjRo0y/A3lxcMNXfCjjAAAAABJRU5ErkJggg==");
  /* ---------------------------------- Toast --------------------------------- */
  --gohub-toast-background-color: rgba(255, 255, 255, 0.85);
  /* --------------------------------- Wizard --------------------------------- */
  --gohub-theme-wizard-nav-item-circle-bg: #fff;
  /* -------------------------------- Card Span ------------------------------- */
  --gohub-card-span-img-bg: #fff;
  --gohub-card-span-img-box-shadow: var(--gohub-box-shadow-sm);
  --gohub-card-span-img-hover-box-shadow: var(--gohub-box-shadow-lg);
  /* ------------------------------ showcase-page ------------------------------ */
  --gohub-setting-toggle-shadow: 0 -7px 14px 0 rgba(65, 69, 88, 0.1), 0 3px 6px 0 rgba(0, 0, 0, 0.07);
  /* -------------------------------- Scrollbar ------------------------------- */
  --gohub-scrollbar-bg: rgba(190, 190, 190, 0.55);
  /* ------------------------------- Falcon Pill ------------------------------ */
  --gohub-nav-pills-falcon-active-bg-color: #fff;
  /* ------------------------------- Hover Background ------------------------------ */
  --gohub-hover-bg-black: #000;
  --gohub-hover-bg-100: #F5F2FC;
  --gohub-hover-bg-200: #f2f2f2;
  --gohub-hover-bg-300: #E7E4EE;
  --gohub-hover-bg-400: #bebebe;
  --gohub-hover-bg-500: #949494;
  --gohub-hover-bg-600: #7F7F7F;
  --gohub-hover-bg-700: #717075;
  --gohub-hover-bg-800: #5E5D61;
  --gohub-hover-bg-900: #403F42;
  --gohub-hover-bg-1000: #212240;
  --gohub-hover-bg-1100: #1c1c1c;
  --gohub-hover-bg-white: #fff;
  /* ------------------------------- Custom disabled button ------------------------------ */
  --gohub-btn-disabled-custom-background: rgba(245, 242, 252, 0.65);
  --gohub-btn-disabled-custom-color: rgba(28, 28, 28, 0.3);
}

/*-----------------------------------------------
|   Bootstrap Styles
-----------------------------------------------*/
*,
*::before,
*::after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--gohub-body-font-family);
  font-size: var(--gohub-body-font-size);
  font-weight: var(--gohub-body-font-weight);
  line-height: var(--gohub-body-line-height);
  color: var(--gohub-body-color);
  text-align: var(--gohub-body-text-align);
  background-color: var(--gohub-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: var(--gohub-border-color);
  background-color: currentColor;
  border: 0;
  opacity: 1;
}

hr:not([size]) {
  height: 1px;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-family: "Baloo Bhaijaan 2", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-weight: 700;
  line-height: 1.2;
  color: var(--gohub-headings-color);
}

h1, .h1 {
  font-size: calc(1.4407rem + 2.2884vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 3.157rem;
  }
}

h2, .h2 {
  font-size: calc(1.3619rem + 1.3428vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2.369rem;
  }
}

h3, .h3 {
  font-size: calc(1.3027rem + 0.6324vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.777rem;
  }
}

h4, .h4 {
  font-size: calc(1.2583rem + 0.0996vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.333rem;
  }
}

h5, .h5 {
  font-size: 1rem;
}

h6, .h6 {
  font-size: 0.75rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-bs-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: 800;
}

small, .small {
  font-size: 75%;
}

mark, .mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #6f42c1;
  text-decoration: none;
}
a:hover {
  color: #59359a;
  text-decoration: underline;
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--gohub-font-monospace);
  font-size: 1em;
  direction: ltr /* rtl:ignore */;
  unicode-bidi: bidi-override;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 75%;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 75%;
  color: #D032D0;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 75%;
  color: #fff;
  background-color: #403F42;
  border-radius: 0.3rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
  font-weight: 700;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #949494;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]::-webkit-calendar-picker-indicator {
  display: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: calc(1.2583rem + 0.0996vw);
  font-weight: 400;
}
@media (min-width: 1200px) {
  .lead {
    font-size: 1.333rem;
  }
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 900;
  line-height: 1;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 75%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: calc(1.2583rem + 0.0996vw);
}
@media (min-width: 1200px) {
  .blockquote {
    font-size: 1.333rem;
  }
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 75%;
  color: #7F7F7F;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--gohub-thumbnail-bg);
  border: 3px solid var(--gohub-thumbnail-bg);
  border-radius: 0.5rem;
  -webkit-box-shadow: var(--gohub-box-shadow-sm);
  box-shadow: var(--gohub-box-shadow-sm);
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 75%;
  color: #7F7F7F;
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: var(--gohub-gutter-x, 1rem);
  padding-left: var(--gohub-gutter-x, 1rem);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 540px) {
  .container-sm, .container {
    max-width: 576px;
  }
}
@media (min-width: 720px) {
  .container-md, .container-sm, .container {
    max-width: 768px;
  }
}
@media (min-width: 960px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 992px;
  }
}
@media (min-width: 1140px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1100px;
  }
}
@media (min-width: 1440px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1200px;
  }
}
.row {
  --gohub-gutter-x: 2rem;
  --gohub-gutter-y: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--gohub-gutter-y));
  margin-right: calc(-.5 * var(--gohub-gutter-x));
  margin-left: calc(-.5 * var(--gohub-gutter-x));
}
.row > * {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--gohub-gutter-x) * .5);
  padding-left: calc(var(--gohub-gutter-x) * .5);
  margin-top: var(--gohub-gutter-y);
}

.col {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
}

.row-cols-auto > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 16.6666666667%;
}

.col-auto {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --gohub-gutter-x: 0;
}

.g-0,
.gy-0 {
  --gohub-gutter-y: 0;
}

.g-1,
.gx-1 {
  --gohub-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
  --gohub-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
  --gohub-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
  --gohub-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
  --gohub-gutter-x: 1rem;
}

.g-3,
.gy-3 {
  --gohub-gutter-y: 1rem;
}

.g-4,
.gx-4 {
  --gohub-gutter-x: 1.8rem;
}

.g-4,
.gy-4 {
  --gohub-gutter-y: 1.8rem;
}

.g-5,
.gx-5 {
  --gohub-gutter-x: 3rem;
}

.g-5,
.gy-5 {
  --gohub-gutter-y: 3rem;
}

.g-6,
.gx-6 {
  --gohub-gutter-x: 4rem;
}

.g-6,
.gy-6 {
  --gohub-gutter-y: 4rem;
}

.g-7,
.gx-7 {
  --gohub-gutter-x: 5rem;
}

.g-7,
.gy-7 {
  --gohub-gutter-y: 5rem;
}

.g-8,
.gx-8 {
  --gohub-gutter-x: 7.5rem;
}

.g-8,
.gy-8 {
  --gohub-gutter-y: 7.5rem;
}

.g-9,
.gx-9 {
  --gohub-gutter-x: 10rem;
}

.g-9,
.gy-9 {
  --gohub-gutter-y: 10rem;
}

.g-10,
.gx-10 {
  --gohub-gutter-x: 12.5rem;
}

.g-10,
.gy-10 {
  --gohub-gutter-y: 12.5rem;
}

.g-11,
.gx-11 {
  --gohub-gutter-x: 15rem;
}

.g-11,
.gy-11 {
  --gohub-gutter-y: 15rem;
}

@media (min-width: 540px) {
  .col-sm {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }

  .row-cols-sm-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-sm-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-sm-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-sm-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-sm-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-sm-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-sm-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-sm-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .col-sm-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-sm-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .col-sm-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-sm-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-sm-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .col-sm-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-sm-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-sm-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }

  .col-sm-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-sm-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-sm-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.33333333%;
  }

  .offset-sm-2 {
    margin-left: 16.66666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.33333333%;
  }

  .offset-sm-5 {
    margin-left: 41.66666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.33333333%;
  }

  .offset-sm-8 {
    margin-left: 66.66666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.33333333%;
  }

  .offset-sm-11 {
    margin-left: 91.66666667%;
  }

  .g-sm-0,
.gx-sm-0 {
    --gohub-gutter-x: 0;
  }

  .g-sm-0,
.gy-sm-0 {
    --gohub-gutter-y: 0;
  }

  .g-sm-1,
.gx-sm-1 {
    --gohub-gutter-x: 0.25rem;
  }

  .g-sm-1,
.gy-sm-1 {
    --gohub-gutter-y: 0.25rem;
  }

  .g-sm-2,
.gx-sm-2 {
    --gohub-gutter-x: 0.5rem;
  }

  .g-sm-2,
.gy-sm-2 {
    --gohub-gutter-y: 0.5rem;
  }

  .g-sm-3,
.gx-sm-3 {
    --gohub-gutter-x: 1rem;
  }

  .g-sm-3,
.gy-sm-3 {
    --gohub-gutter-y: 1rem;
  }

  .g-sm-4,
.gx-sm-4 {
    --gohub-gutter-x: 1.8rem;
  }

  .g-sm-4,
.gy-sm-4 {
    --gohub-gutter-y: 1.8rem;
  }

  .g-sm-5,
.gx-sm-5 {
    --gohub-gutter-x: 3rem;
  }

  .g-sm-5,
.gy-sm-5 {
    --gohub-gutter-y: 3rem;
  }

  .g-sm-6,
.gx-sm-6 {
    --gohub-gutter-x: 4rem;
  }

  .g-sm-6,
.gy-sm-6 {
    --gohub-gutter-y: 4rem;
  }

  .g-sm-7,
.gx-sm-7 {
    --gohub-gutter-x: 5rem;
  }

  .g-sm-7,
.gy-sm-7 {
    --gohub-gutter-y: 5rem;
  }

  .g-sm-8,
.gx-sm-8 {
    --gohub-gutter-x: 7.5rem;
  }

  .g-sm-8,
.gy-sm-8 {
    --gohub-gutter-y: 7.5rem;
  }

  .g-sm-9,
.gx-sm-9 {
    --gohub-gutter-x: 10rem;
  }

  .g-sm-9,
.gy-sm-9 {
    --gohub-gutter-y: 10rem;
  }

  .g-sm-10,
.gx-sm-10 {
    --gohub-gutter-x: 12.5rem;
  }

  .g-sm-10,
.gy-sm-10 {
    --gohub-gutter-y: 12.5rem;
  }

  .g-sm-11,
.gx-sm-11 {
    --gohub-gutter-x: 15rem;
  }

  .g-sm-11,
.gy-sm-11 {
    --gohub-gutter-y: 15rem;
  }
}
@media (min-width: 720px) {
  .col-md {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }

  .row-cols-md-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-md-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-md-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-md-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-md-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-md-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-md-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-md-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .col-md-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-md-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .col-md-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-md-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-md-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .col-md-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-md-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-md-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }

  .col-md-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-md-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-md-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.33333333%;
  }

  .offset-md-2 {
    margin-left: 16.66666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.33333333%;
  }

  .offset-md-5 {
    margin-left: 41.66666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.33333333%;
  }

  .offset-md-8 {
    margin-left: 66.66666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.33333333%;
  }

  .offset-md-11 {
    margin-left: 91.66666667%;
  }

  .g-md-0,
.gx-md-0 {
    --gohub-gutter-x: 0;
  }

  .g-md-0,
.gy-md-0 {
    --gohub-gutter-y: 0;
  }

  .g-md-1,
.gx-md-1 {
    --gohub-gutter-x: 0.25rem;
  }

  .g-md-1,
.gy-md-1 {
    --gohub-gutter-y: 0.25rem;
  }

  .g-md-2,
.gx-md-2 {
    --gohub-gutter-x: 0.5rem;
  }

  .g-md-2,
.gy-md-2 {
    --gohub-gutter-y: 0.5rem;
  }

  .g-md-3,
.gx-md-3 {
    --gohub-gutter-x: 1rem;
  }

  .g-md-3,
.gy-md-3 {
    --gohub-gutter-y: 1rem;
  }

  .g-md-4,
.gx-md-4 {
    --gohub-gutter-x: 1.8rem;
  }

  .g-md-4,
.gy-md-4 {
    --gohub-gutter-y: 1.8rem;
  }

  .g-md-5,
.gx-md-5 {
    --gohub-gutter-x: 3rem;
  }

  .g-md-5,
.gy-md-5 {
    --gohub-gutter-y: 3rem;
  }

  .g-md-6,
.gx-md-6 {
    --gohub-gutter-x: 4rem;
  }

  .g-md-6,
.gy-md-6 {
    --gohub-gutter-y: 4rem;
  }

  .g-md-7,
.gx-md-7 {
    --gohub-gutter-x: 5rem;
  }

  .g-md-7,
.gy-md-7 {
    --gohub-gutter-y: 5rem;
  }

  .g-md-8,
.gx-md-8 {
    --gohub-gutter-x: 7.5rem;
  }

  .g-md-8,
.gy-md-8 {
    --gohub-gutter-y: 7.5rem;
  }

  .g-md-9,
.gx-md-9 {
    --gohub-gutter-x: 10rem;
  }

  .g-md-9,
.gy-md-9 {
    --gohub-gutter-y: 10rem;
  }

  .g-md-10,
.gx-md-10 {
    --gohub-gutter-x: 12.5rem;
  }

  .g-md-10,
.gy-md-10 {
    --gohub-gutter-y: 12.5rem;
  }

  .g-md-11,
.gx-md-11 {
    --gohub-gutter-x: 15rem;
  }

  .g-md-11,
.gy-md-11 {
    --gohub-gutter-y: 15rem;
  }
}
@media (min-width: 960px) {
  .col-lg {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }

  .row-cols-lg-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-lg-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-lg-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-lg-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-lg-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-lg-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-lg-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-lg-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .col-lg-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-lg-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .col-lg-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-lg-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-lg-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .col-lg-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-lg-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-lg-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }

  .col-lg-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-lg-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-lg-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.33333333%;
  }

  .offset-lg-2 {
    margin-left: 16.66666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.33333333%;
  }

  .offset-lg-5 {
    margin-left: 41.66666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.33333333%;
  }

  .offset-lg-8 {
    margin-left: 66.66666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.33333333%;
  }

  .offset-lg-11 {
    margin-left: 91.66666667%;
  }

  .g-lg-0,
.gx-lg-0 {
    --gohub-gutter-x: 0;
  }

  .g-lg-0,
.gy-lg-0 {
    --gohub-gutter-y: 0;
  }

  .g-lg-1,
.gx-lg-1 {
    --gohub-gutter-x: 0.25rem;
  }

  .g-lg-1,
.gy-lg-1 {
    --gohub-gutter-y: 0.25rem;
  }

  .g-lg-2,
.gx-lg-2 {
    --gohub-gutter-x: 0.5rem;
  }

  .g-lg-2,
.gy-lg-2 {
    --gohub-gutter-y: 0.5rem;
  }

  .g-lg-3,
.gx-lg-3 {
    --gohub-gutter-x: 1rem;
  }

  .g-lg-3,
.gy-lg-3 {
    --gohub-gutter-y: 1rem;
  }

  .g-lg-4,
.gx-lg-4 {
    --gohub-gutter-x: 1.8rem;
  }

  .g-lg-4,
.gy-lg-4 {
    --gohub-gutter-y: 1.8rem;
  }

  .g-lg-5,
.gx-lg-5 {
    --gohub-gutter-x: 3rem;
  }

  .g-lg-5,
.gy-lg-5 {
    --gohub-gutter-y: 3rem;
  }

  .g-lg-6,
.gx-lg-6 {
    --gohub-gutter-x: 4rem;
  }

  .g-lg-6,
.gy-lg-6 {
    --gohub-gutter-y: 4rem;
  }

  .g-lg-7,
.gx-lg-7 {
    --gohub-gutter-x: 5rem;
  }

  .g-lg-7,
.gy-lg-7 {
    --gohub-gutter-y: 5rem;
  }

  .g-lg-8,
.gx-lg-8 {
    --gohub-gutter-x: 7.5rem;
  }

  .g-lg-8,
.gy-lg-8 {
    --gohub-gutter-y: 7.5rem;
  }

  .g-lg-9,
.gx-lg-9 {
    --gohub-gutter-x: 10rem;
  }

  .g-lg-9,
.gy-lg-9 {
    --gohub-gutter-y: 10rem;
  }

  .g-lg-10,
.gx-lg-10 {
    --gohub-gutter-x: 12.5rem;
  }

  .g-lg-10,
.gy-lg-10 {
    --gohub-gutter-y: 12.5rem;
  }

  .g-lg-11,
.gx-lg-11 {
    --gohub-gutter-x: 15rem;
  }

  .g-lg-11,
.gy-lg-11 {
    --gohub-gutter-y: 15rem;
  }
}
@media (min-width: 1140px) {
  .col-xl {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }

  .row-cols-xl-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xl-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xl-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xl-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xl-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xl-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xl-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-xl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .col-xl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xl-11 {
    margin-left: 91.66666667%;
  }

  .g-xl-0,
.gx-xl-0 {
    --gohub-gutter-x: 0;
  }

  .g-xl-0,
.gy-xl-0 {
    --gohub-gutter-y: 0;
  }

  .g-xl-1,
.gx-xl-1 {
    --gohub-gutter-x: 0.25rem;
  }

  .g-xl-1,
.gy-xl-1 {
    --gohub-gutter-y: 0.25rem;
  }

  .g-xl-2,
.gx-xl-2 {
    --gohub-gutter-x: 0.5rem;
  }

  .g-xl-2,
.gy-xl-2 {
    --gohub-gutter-y: 0.5rem;
  }

  .g-xl-3,
.gx-xl-3 {
    --gohub-gutter-x: 1rem;
  }

  .g-xl-3,
.gy-xl-3 {
    --gohub-gutter-y: 1rem;
  }

  .g-xl-4,
.gx-xl-4 {
    --gohub-gutter-x: 1.8rem;
  }

  .g-xl-4,
.gy-xl-4 {
    --gohub-gutter-y: 1.8rem;
  }

  .g-xl-5,
.gx-xl-5 {
    --gohub-gutter-x: 3rem;
  }

  .g-xl-5,
.gy-xl-5 {
    --gohub-gutter-y: 3rem;
  }

  .g-xl-6,
.gx-xl-6 {
    --gohub-gutter-x: 4rem;
  }

  .g-xl-6,
.gy-xl-6 {
    --gohub-gutter-y: 4rem;
  }

  .g-xl-7,
.gx-xl-7 {
    --gohub-gutter-x: 5rem;
  }

  .g-xl-7,
.gy-xl-7 {
    --gohub-gutter-y: 5rem;
  }

  .g-xl-8,
.gx-xl-8 {
    --gohub-gutter-x: 7.5rem;
  }

  .g-xl-8,
.gy-xl-8 {
    --gohub-gutter-y: 7.5rem;
  }

  .g-xl-9,
.gx-xl-9 {
    --gohub-gutter-x: 10rem;
  }

  .g-xl-9,
.gy-xl-9 {
    --gohub-gutter-y: 10rem;
  }

  .g-xl-10,
.gx-xl-10 {
    --gohub-gutter-x: 12.5rem;
  }

  .g-xl-10,
.gy-xl-10 {
    --gohub-gutter-y: 12.5rem;
  }

  .g-xl-11,
.gx-xl-11 {
    --gohub-gutter-x: 15rem;
  }

  .g-xl-11,
.gy-xl-11 {
    --gohub-gutter-y: 15rem;
  }
}
@media (min-width: 1440px) {
  .col-xxl {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }

  .row-cols-xxl-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xxl-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xxl-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xxl-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xxl-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xxl-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xxl-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-xxl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }

  .col-xxl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xxl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xxl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xxl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xxl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xxl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xxl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xxl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xxl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xxl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xxl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }

  .offset-xxl-0 {
    margin-left: 0;
  }

  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }

  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }

  .offset-xxl-3 {
    margin-left: 25%;
  }

  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }

  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }

  .offset-xxl-6 {
    margin-left: 50%;
  }

  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }

  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }

  .offset-xxl-9 {
    margin-left: 75%;
  }

  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }

  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }

  .g-xxl-0,
.gx-xxl-0 {
    --gohub-gutter-x: 0;
  }

  .g-xxl-0,
.gy-xxl-0 {
    --gohub-gutter-y: 0;
  }

  .g-xxl-1,
.gx-xxl-1 {
    --gohub-gutter-x: 0.25rem;
  }

  .g-xxl-1,
.gy-xxl-1 {
    --gohub-gutter-y: 0.25rem;
  }

  .g-xxl-2,
.gx-xxl-2 {
    --gohub-gutter-x: 0.5rem;
  }

  .g-xxl-2,
.gy-xxl-2 {
    --gohub-gutter-y: 0.5rem;
  }

  .g-xxl-3,
.gx-xxl-3 {
    --gohub-gutter-x: 1rem;
  }

  .g-xxl-3,
.gy-xxl-3 {
    --gohub-gutter-y: 1rem;
  }

  .g-xxl-4,
.gx-xxl-4 {
    --gohub-gutter-x: 1.8rem;
  }

  .g-xxl-4,
.gy-xxl-4 {
    --gohub-gutter-y: 1.8rem;
  }

  .g-xxl-5,
.gx-xxl-5 {
    --gohub-gutter-x: 3rem;
  }

  .g-xxl-5,
.gy-xxl-5 {
    --gohub-gutter-y: 3rem;
  }

  .g-xxl-6,
.gx-xxl-6 {
    --gohub-gutter-x: 4rem;
  }

  .g-xxl-6,
.gy-xxl-6 {
    --gohub-gutter-y: 4rem;
  }

  .g-xxl-7,
.gx-xxl-7 {
    --gohub-gutter-x: 5rem;
  }

  .g-xxl-7,
.gy-xxl-7 {
    --gohub-gutter-y: 5rem;
  }

  .g-xxl-8,
.gx-xxl-8 {
    --gohub-gutter-x: 7.5rem;
  }

  .g-xxl-8,
.gy-xxl-8 {
    --gohub-gutter-y: 7.5rem;
  }

  .g-xxl-9,
.gx-xxl-9 {
    --gohub-gutter-x: 10rem;
  }

  .g-xxl-9,
.gy-xxl-9 {
    --gohub-gutter-y: 10rem;
  }

  .g-xxl-10,
.gx-xxl-10 {
    --gohub-gutter-x: 12.5rem;
  }

  .g-xxl-10,
.gy-xxl-10 {
    --gohub-gutter-y: 12.5rem;
  }

  .g-xxl-11,
.gx-xxl-11 {
    --gohub-gutter-x: 15rem;
  }

  .g-xxl-11,
.gy-xxl-11 {
    --gohub-gutter-y: 15rem;
  }
}
.table {
  --gohub-table-bg: transparent;
  --gohub-table-accent-bg: transparent;
  --gohub-table-striped-color: #717075;
  --gohub-table-striped-bg: rgba(0, 0, 0, 0.05);
  --gohub-table-active-color: #717075;
  --gohub-table-active-bg: rgba(0, 0, 0, 0.1);
  --gohub-table-hover-color: #717075;
  --gohub-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: #717075;
  vertical-align: top;
  border-color: var(--gohub-table-border-color);
}
.table > :not(caption) > * > * {
  padding: 0.75rem 0.75rem;
  background-color: var(--gohub-table-bg);
  border-bottom-width: 1px;
  -webkit-box-shadow: inset 0 0 0 9999px var(--gohub-table-accent-bg);
  box-shadow: inset 0 0 0 9999px var(--gohub-table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}
.table > :not(:first-child) {
  border-top: 2px solid inherit;
}

.caption-top {
  caption-side: top;
}

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.table-striped > tbody > tr:nth-of-type(even) > * {
  --gohub-table-accent-bg: var(--gohub-table-striped-bg);
  color: var(--gohub-table-striped-color);
}

.table-active {
  --gohub-table-accent-bg: var(--gohub-table-active-bg);
  color: var(--gohub-table-active-color);
}

.table-hover > tbody > tr:hover > * {
  --gohub-table-accent-bg: var(--gohub-table-hover-bg);
  color: var(--gohub-table-hover-color);
}

.table-primary {
  --gohub-table-bg: #FFEBEE;
  --gohub-table-striped-bg: #FFD7DA;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #FFCDD2;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #d8d0e8;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #d5cde4;
}

.table-secondary {
  --gohub-table-bg: #f6d6f6;
  --gohub-table-striped-bg: #eed0ef;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #e7cae7;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #ebcdeb;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #e7cae7;
}

.table-success {
  --gohub-table-bg: #e5f6d3;
  --gohub-table-striped-bg: #deeecd;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #d8e7c8;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #dbebca;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #d8e7c8;
}

.table-info {
  --gohub-table-bg: #d2d0ef;
  --gohub-table-striped-bg: #cccae8;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #c6c5e1;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #c9c7e4;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #c6c5e1;
}

.table-warning {
  --gohub-table-bg: #fde5d4;
  --gohub-table-striped-bg: #f5dece;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #edd7c9;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #f1dbcb;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #edd7c9;
}

.table-danger {
  --gohub-table-bg: #f6ccd1;
  --gohub-table-striped-bg: #eec6cb;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #e7c1c6;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #ebc4c9;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #e7c1c6;
}

.table-light {
  --gohub-table-bg: #F5F2FC;
  --gohub-table-striped-bg: #edebf4;
  --gohub-table-striped-color: #5E5D61;
  --gohub-table-active-bg: #e6e3ed;
  --gohub-table-active-color: #5E5D61;
  --gohub-table-hover-bg: #eae7f0;
  --gohub-table-hover-color: #5E5D61;
  color: #5E5D61;
  border-color: #e6e3ed;
}

.table-dark {
  --gohub-table-bg: #1c1c1c;
  --gohub-table-striped-bg: #272727;
  --gohub-table-striped-color: #fff;
  --gohub-table-active-bg: #333333;
  --gohub-table-active-color: #fff;
  --gohub-table-hover-bg: #2d2d2d;
  --gohub-table-hover-color: #fff;
  color: #fff;
  border-color: #333333;
}

@media (max-width: 539.98px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 719.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 959.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1139.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1439.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.table-responsive-xxl {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.form-label {
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
}

.col-form-label {
  padding-top: calc(0.8rem + 1px);
  padding-bottom: calc(0.8rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.45;
}

.col-form-label-lg {
  padding-top: calc(1.2rem + 1px);
  padding-bottom: calc(1.2rem + 1px);
  font-size: calc(1.2583rem + 0.0996vw);
}
@media (min-width: 1200px) {
  .col-form-label-lg {
    font-size: 1.333rem;
  }
}

.col-form-label-sm {
  padding-top: calc(0.4rem + 1px);
  padding-bottom: calc(0.4rem + 1px);
  font-size: 0.75rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 75%;
  color: #949494;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.8rem 0.8rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.45;
  color: var(--gohub-input-color);
  background-color: var(--gohub-input-bg);
  background-clip: padding-box;
  border: 1px solid var(--gohub-input-border-color);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.5rem;
  -webkit-box-shadow: "null";
  box-shadow: "null";
  -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: var(--gohub-input-color);
  background-color: var(--gohub-input-bg);
  border-color: #b7a1e0;
  outline: 0;
  -webkit-box-shadow: "null", 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: "null", 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.form-control::-webkit-date-and-time-value {
  height: 1.45em;
}
.form-control::-webkit-input-placeholder {
  color: var(--gohub-600);
  opacity: 1;
}
.form-control::-moz-placeholder {
  color: var(--gohub-600);
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: var(--gohub-600);
  opacity: 1;
}
.form-control::-ms-input-placeholder {
  color: var(--gohub-600);
  opacity: 1;
}
.form-control::placeholder {
  color: var(--gohub-600);
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: var(--gohub-200);
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.8rem 0.8rem;
  margin: -0.8rem -0.8rem;
  -webkit-margin-end: 0.8rem;
  margin-inline-end: 0.8rem;
  color: #E7E4EE;
  background-color: #403F42;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: #3d3c3f;
}
.form-control::-webkit-file-upload-button {
  padding: 0.8rem 0.8rem;
  margin: -0.8rem -0.8rem;
  -webkit-margin-end: 0.8rem;
  margin-inline-end: 0.8rem;
  color: #E7E4EE;
  background-color: #403F42;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: #3d3c3f;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.8rem 0;
  margin-bottom: 0;
  line-height: 1.45;
  color: #717075;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.45em + 0.8rem + 2px);
  padding: 0.4rem 0.4rem;
  font-size: 0.75rem;
  border-radius: 0.3rem;
}
.form-control-sm::file-selector-button {
  padding: 0.4rem 0.4rem;
  margin: -0.4rem -0.4rem;
  -webkit-margin-end: 0.4rem;
  margin-inline-end: 0.4rem;
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.4rem 0.4rem;
  margin: -0.4rem -0.4rem;
  -webkit-margin-end: 0.4rem;
  margin-inline-end: 0.4rem;
}

.form-control-lg {
  min-height: calc(1.45em + 2.4rem + 2px);
  padding: 1.2rem 1.2rem;
  font-size: calc(1.2583rem + 0.0996vw);
  border-radius: 0.7rem;
}
@media (min-width: 1200px) {
  .form-control-lg {
    font-size: 1.333rem;
  }
}
.form-control-lg::file-selector-button {
  padding: 1.2rem 1.2rem;
  margin: -1.2rem -1.2rem;
  -webkit-margin-end: 1.2rem;
  margin-inline-end: 1.2rem;
}
.form-control-lg::-webkit-file-upload-button {
  padding: 1.2rem 1.2rem;
  margin: -1.2rem -1.2rem;
  -webkit-margin-end: 1.2rem;
  margin-inline-end: 1.2rem;
}

textarea.form-control {
  min-height: calc(1.45em + 1.6rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.45em + 0.8rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.45em + 2.4rem + 2px);
}

.form-control-color {
  width: 3rem;
  height: auto;
  padding: 0.8rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  height: 1.45em;
  border-radius: 0.5rem;
}
.form-control-color::-webkit-color-swatch {
  height: 1.45em;
  border-radius: 0.5rem;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.8rem 2.4rem 0.8rem 0.8rem;
  -moz-padding-start: calc(0.8rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.45;
  color: var(--gohub-input-color);
  background-color: var(--gohub-input-bg);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235E5D61' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.8rem center;
  background-size: 16px 12px;
  border: 1px solid var(--gohub-input-border-color);
  border-radius: 0.5rem;
  -webkit-box-shadow: var(--gohub-box-shadow-inset);
  box-shadow: var(--gohub-box-shadow-inset);
  -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.form-select:focus {
  border-color: #b7a1e0;
  outline: 0;
  -webkit-box-shadow: var(--gohub-box-shadow-inset), 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: var(--gohub-box-shadow-inset), 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 0.8rem;
  background-image: none;
}
.form-select:disabled {
  background-color: var(--gohub-200);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--gohub-input-color);
}

.form-select-sm {
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
  padding-left: 0.4rem;
  font-size: 0.75rem;
  border-radius: 0.3rem;
}

.form-select-lg {
  padding-top: 1.2rem;
  padding-bottom: 1.2rem;
  padding-left: 1.2rem;
  font-size: calc(1.2583rem + 0.0996vw);
  border-radius: 0.7rem;
}
@media (min-width: 1200px) {
  .form-select-lg {
    font-size: 1.333rem;
  }
}

.form-check {
  display: block;
  min-height: 1.45rem;
  padding-left: 1.5em;
  margin-bottom: 0.34375rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.225em;
  vertical-align: top;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid var(--gohub-form-check-input-border-color);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  -webkit-filter: brightness(90%);
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #b7a1e0;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.form-check-input:checked {
  background-color: #6f42c1;
  border-color: #6f42c1;
}
.form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #6f42c1;
  border-color: #6f42c1;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  -webkit-filter: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23949494'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  -webkit-transition: background-position 0.15s ease-in-out;
  -o-transition: background-position 0.15s ease-in-out;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23b7a1e0'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check:disabled + .btn {
  pointer-events: none;
  -webkit-filter: none;
  filter: none;
  opacity: 0.65;
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  -webkit-box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #6f42c1;
  border: 0;
  border-radius: 1rem;
  -webkit-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #d4c6ec;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #E7E4EE;
  border-color: transparent;
  border-radius: 1rem;
  -webkit-box-shadow: var(--gohub-box-shadow-inset);
  box-shadow: var(--gohub-box-shadow-inset);
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #6f42c1;
  border: 0;
  border-radius: 1rem;
  box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #d4c6ec;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #E7E4EE;
  border-color: transparent;
  border-radius: 1rem;
  box-shadow: var(--gohub-box-shadow-inset);
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #949494;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #949494;
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.8rem;
  pointer-events: none;
  border: 1px solid transparent;
  -webkit-transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  -o-transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.form-floating > .form-control {
  padding: 1rem 0.8rem;
}
.form-floating > .form-control::-webkit-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control:-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-ms-input-placeholder) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:not(:-ms-input-placeholder) ~ label {
  opacity: 0.65;
  -ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  -ms-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus {
  z-index: 3;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 3;
}

.input-group-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.8rem 0.8rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.45;
  color: var(--gohub-input-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--gohub-200);
  border: 1px solid var(--gohub-input-border-color);
  border-radius: 0.5rem;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  padding: 1.2rem 1.2rem;
  font-size: calc(1.2583rem + 0.0996vw);
  border-radius: 0.7rem;
}
@media (min-width: 1200px) {
  .input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
    font-size: 1.333rem;
  }
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  padding: 0.4rem 0.4rem;
  font-size: 0.75rem;
  border-radius: 0.3rem;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 3.2rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 75%;
  color: #7ed321;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.5rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.75rem;
  color: #5E5D61;
  background-color: rgba(126, 211, 33, 0.9);
  border-radius: 0.5rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #7ed321;
  padding-right: calc(1.45em + 1.6rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%237ed321' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.3625em + 0.4rem) center;
  background-size: calc(0.725em + 0.8rem) calc(0.725em + 0.8rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #7ed321;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.45em + 1.6rem);
  background-position: top calc(0.3625em + 0.4rem) right calc(0.3625em + 0.4rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #7ed321;
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  padding-right: 4.4rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235E5D61' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%237ed321' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.8rem center, center right 2.4rem;
  background-size: 16px 12px, calc(0.725em + 0.8rem) calc(0.725em + 0.8rem);
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #7ed321;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #7ed321;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #7ed321;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(126, 211, 33, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #7ed321;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group .form-control:valid, .input-group .form-control.is-valid,
.was-validated .input-group .form-select:valid,
.input-group .form-select.is-valid {
  z-index: 1;
}
.was-validated .input-group .form-control:valid:focus, .input-group .form-control.is-valid:focus,
.was-validated .input-group .form-select:valid:focus,
.input-group .form-select.is-valid:focus {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 75%;
  color: #d0021b;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.5rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.75rem;
  color: #fff;
  background-color: rgba(208, 2, 27, 0.9);
  border-radius: 0.5rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #d0021b;
  padding-right: calc(1.45em + 1.6rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d0021b'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d0021b' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.3625em + 0.4rem) center;
  background-size: calc(0.725em + 0.8rem) calc(0.725em + 0.8rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #d0021b;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.45em + 1.6rem);
  background-position: top calc(0.3625em + 0.4rem) right calc(0.3625em + 0.4rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #d0021b;
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 4.4rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%235E5D61' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d0021b'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d0021b' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.8rem center, center right 2.4rem;
  background-size: 16px 12px, calc(0.725em + 0.8rem) calc(0.725em + 0.8rem);
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #d0021b;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #d0021b;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #d0021b;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(208, 2, 27, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #d0021b;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group .form-control:invalid, .input-group .form-control.is-invalid,
.was-validated .input-group .form-select:invalid,
.input-group .form-select.is-invalid {
  z-index: 2;
}
.was-validated .input-group .form-control:invalid:focus, .input-group .form-control.is-invalid:focus,
.was-validated .input-group .form-select:invalid:focus,
.input-group .form-select.is-invalid:focus {
  z-index: 3;
}

.btn {
  display: inline-block;
  font-weight: 700;
  line-height: 1.45;
  color: #717075;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.8rem 2.5rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  /* -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out; */
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.btn:hover {
  color: #717075;
  text-decoration: none;
}
.btn-check:focus + .btn, .btn:focus {
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}
.btn-check:checked + .btn, .btn-check:active + .btn, .btn:active, .btn.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-check:checked + .btn:focus, .btn-check:active + .btn:focus, .btn:active:focus, .btn.active:focus {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25), inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25), inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  pointer-events: none;
  opacity: 0.65;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.btn-primary {
  color: #fff;
  background-color: #6f42c1;
  border-color: #6f42c1;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-primary:hover {
  color: #fff;
  background-color: #5e38a4;
  border-color: #59359a;
}
.btn-check:focus + .btn-primary, .btn-primary:focus {
  color: #fff;
  background-color: #5e38a4;
  border-color: #59359a;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(133, 94, 202, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(133, 94, 202, 0.5);
}
.btn-check:checked + .btn-primary, .btn-check:active + .btn-primary, .btn-primary:active, .btn-primary.active, .show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #59359a;
  border-color: #533291;
}
.btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-primary:focus, .btn-primary:active:focus, .btn-primary.active:focus, .show > .btn-primary.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(133, 94, 202, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(133, 94, 202, 0.5);
}
.btn-primary:disabled, .btn-primary.disabled {
  color: #fff;
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.btn-secondary {
  color: #fff;
  background-color: #D032D0;
  border-color: #D032D0;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-secondary:hover {
  color: #fff;
  background-color: #b12bb1;
  border-color: #a628a6;
}
.btn-check:focus + .btn-secondary, .btn-secondary:focus {
  color: #fff;
  background-color: #b12bb1;
  border-color: #a628a6;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(215, 81, 215, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(215, 81, 215, 0.5);
}
.btn-check:checked + .btn-secondary, .btn-check:active + .btn-secondary, .btn-secondary:active, .btn-secondary.active, .show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #a628a6;
  border-color: #9c269c;
}
.btn-check:checked + .btn-secondary:focus, .btn-check:active + .btn-secondary:focus, .btn-secondary:active:focus, .btn-secondary.active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(215, 81, 215, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(215, 81, 215, 0.5);
}
.btn-secondary:disabled, .btn-secondary.disabled {
  color: #fff;
  background-color: #D032D0;
  border-color: #D032D0;
}

.btn-success {
  color: #5E5D61;
  background-color: #7ed321;
  border-color: #7ed321;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-success:hover {
  color: #5E5D61;
  background-color: #91da42;
  border-color: #8bd737;
}
.btn-check:focus + .btn-success, .btn-success:focus {
  color: #5E5D61;
  background-color: #91da42;
  border-color: #8bd737;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(121, 193, 43, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(121, 193, 43, 0.5);
}
.btn-check:checked + .btn-success, .btn-check:active + .btn-success, .btn-success:active, .btn-success.active, .show > .btn-success.dropdown-toggle {
  color: #5E5D61;
  background-color: #98dc4d;
  border-color: #8bd737;
}
.btn-check:checked + .btn-success:focus, .btn-check:active + .btn-success:focus, .btn-success:active:focus, .btn-success.active:focus, .show > .btn-success.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(121, 193, 43, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(121, 193, 43, 0.5);
}
.btn-success:disabled, .btn-success.disabled {
  color: #5E5D61;
  background-color: #7ed321;
  border-color: #7ed321;
}

.btn-info {
  color: #fff;
  background-color: #1C16AF;
  border-color: #1C16AF;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-info:hover {
  color: #fff;
  background-color: #181395;
  border-color: #16128c;
}
.btn-check:focus + .btn-info, .btn-info:focus {
  color: #fff;
  background-color: #181395;
  border-color: #16128c;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(62, 57, 187, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(62, 57, 187, 0.5);
}
.btn-check:checked + .btn-info, .btn-check:active + .btn-info, .btn-info:active, .btn-info.active, .show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #16128c;
  border-color: #151183;
}
.btn-check:checked + .btn-info:focus, .btn-check:active + .btn-info:focus, .btn-info:active:focus, .btn-info.active:focus, .show > .btn-info.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(62, 57, 187, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(62, 57, 187, 0.5);
}
.btn-info:disabled, .btn-info.disabled {
  color: #fff;
  background-color: #1C16AF;
  border-color: #1C16AF;
}

.btn-warning {
  color: #fff;
  background-color: #f37f29;
  border-color: #f37f29;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-warning:hover {
  color: #fff;
  background-color: #cf6c23;
  border-color: #c26621;
}
.btn-check:focus + .btn-warning, .btn-warning:focus {
  color: #fff;
  background-color: #cf6c23;
  border-color: #c26621;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(245, 146, 73, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(245, 146, 73, 0.5);
}
.btn-check:checked + .btn-warning, .btn-check:active + .btn-warning, .btn-warning:active, .btn-warning.active, .show > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #c26621;
  border-color: #b65f1f;
}
.btn-check:checked + .btn-warning:focus, .btn-check:active + .btn-warning:focus, .btn-warning:active:focus, .btn-warning.active:focus, .show > .btn-warning.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(245, 146, 73, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(245, 146, 73, 0.5);
}
.btn-warning:disabled, .btn-warning.disabled {
  color: #fff;
  background-color: #f37f29;
  border-color: #f37f29;
}

.btn-danger {
  color: #fff;
  background-color: #d0021b;
  border-color: #d0021b;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-danger:hover {
  color: #fff;
  background-color: #b10217;
  border-color: #a60216;
}
.btn-check:focus + .btn-danger, .btn-danger:focus {
  color: #fff;
  background-color: #b10217;
  border-color: #a60216;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(215, 40, 61, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(215, 40, 61, 0.5);
}
.btn-check:checked + .btn-danger, .btn-check:active + .btn-danger, .btn-danger:active, .btn-danger.active, .show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #a60216;
  border-color: #9c0214;
}
.btn-check:checked + .btn-danger:focus, .btn-check:active + .btn-danger:focus, .btn-danger:active:focus, .btn-danger.active:focus, .show > .btn-danger.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(215, 40, 61, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(215, 40, 61, 0.5);
}
.btn-danger:disabled, .btn-danger.disabled {
  color: #fff;
  background-color: #d0021b;
  border-color: #d0021b;
}

.btn-light {
  color: #5E5D61;
  background-color: #F5F2FC;
  border-color: #F5F2FC;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-light:hover {
  color: #5E5D61;
  background-color: #f7f4fc;
  border-color: #f6f3fc;
}
.btn-check:focus + .btn-light, .btn-light:focus {
  color: #5E5D61;
  background-color: #f7f4fc;
  border-color: #f6f3fc;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(222, 220, 229, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(222, 220, 229, 0.5);
}
.btn-check:checked + .btn-light, .btn-check:active + .btn-light, .btn-light:active, .btn-light.active, .show > .btn-light.dropdown-toggle {
  color: #5E5D61;
  background-color: #f7f5fd;
  border-color: #f6f3fc;
}
.btn-check:checked + .btn-light:focus, .btn-check:active + .btn-light:focus, .btn-light:active:focus, .btn-light.active:focus, .show > .btn-light.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(222, 220, 229, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(222, 220, 229, 0.5);
}
.btn-light:disabled, .btn-light.disabled {
  color: #5E5D61;
  background-color: #F5F2FC;
  border-color: #F5F2FC;
}

.btn-dark {
  color: #fff;
  background-color: #1c1c1c;
  border-color: #1c1c1c;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
}
.btn-dark:hover {
  color: #fff;
  background-color: #181818;
  border-color: #161616;
}
.btn-check:focus + .btn-dark, .btn-dark:focus {
  color: #fff;
  background-color: #181818;
  border-color: #161616;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(62, 62, 62, 0.5);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 0 rgba(62, 62, 62, 0.5);
}
.btn-check:checked + .btn-dark, .btn-check:active + .btn-dark, .btn-dark:active, .btn-dark.active, .show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #161616;
  border-color: #151515;
}
.btn-check:checked + .btn-dark:focus, .btn-check:active + .btn-dark:focus, .btn-dark:active:focus, .btn-dark.active:focus, .show > .btn-dark.dropdown-toggle:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(62, 62, 62, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(62, 62, 62, 0.5);
}
.btn-dark:disabled, .btn-dark.disabled {
  color: #fff;
  background-color: #1c1c1c;
  border-color: #1c1c1c;
}

.btn-outline-primary {
  color: #6f42c1;
  border-color: #6f42c1;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #6f42c1;
  border-color: #6f42c1;
}
.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(111, 66, 193, 0.5);
  box-shadow: 0 0 0 0 rgba(111, 66, 193, 0.5);
}
.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show {
  color: #fff;
  background-color: #6f42c1;
  border-color: #6f42c1;
}
.btn-check:checked + .btn-outline-primary:focus, .btn-check:active + .btn-outline-primary:focus, .btn-outline-primary:active:focus, .btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(111, 66, 193, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(111, 66, 193, 0.5);
}
.btn-outline-primary:disabled, .btn-outline-primary.disabled {
  color: #6f42c1;
  background-color: transparent;
}

.btn-outline-secondary {
  color: #D032D0;
  border-color: #D032D0;
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: #D032D0;
  border-color: #D032D0;
}
.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(208, 50, 208, 0.5);
  box-shadow: 0 0 0 0 rgba(208, 50, 208, 0.5);
}
.btn-check:checked + .btn-outline-secondary, .btn-check:active + .btn-outline-secondary, .btn-outline-secondary:active, .btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show {
  color: #fff;
  background-color: #D032D0;
  border-color: #D032D0;
}
.btn-check:checked + .btn-outline-secondary:focus, .btn-check:active + .btn-outline-secondary:focus, .btn-outline-secondary:active:focus, .btn-outline-secondary.active:focus, .btn-outline-secondary.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(208, 50, 208, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(208, 50, 208, 0.5);
}
.btn-outline-secondary:disabled, .btn-outline-secondary.disabled {
  color: #D032D0;
  background-color: transparent;
}

.btn-outline-success {
  color: #7ed321;
  border-color: #7ed321;
}
.btn-outline-success:hover {
  color: #5E5D61;
  background-color: #7ed321;
  border-color: #7ed321;
}
.btn-check:focus + .btn-outline-success, .btn-outline-success:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(126, 211, 33, 0.5);
  box-shadow: 0 0 0 0 rgba(126, 211, 33, 0.5);
}
.btn-check:checked + .btn-outline-success, .btn-check:active + .btn-outline-success, .btn-outline-success:active, .btn-outline-success.active, .btn-outline-success.dropdown-toggle.show {
  color: #5E5D61;
  background-color: #7ed321;
  border-color: #7ed321;
}
.btn-check:checked + .btn-outline-success:focus, .btn-check:active + .btn-outline-success:focus, .btn-outline-success:active:focus, .btn-outline-success.active:focus, .btn-outline-success.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(126, 211, 33, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(126, 211, 33, 0.5);
}
.btn-outline-success:disabled, .btn-outline-success.disabled {
  color: #7ed321;
  background-color: transparent;
}

.btn-outline-info {
  color: #1C16AF;
  border-color: #1C16AF;
}
.btn-outline-info:hover {
  color: #fff;
  background-color: #1C16AF;
  border-color: #1C16AF;
}
.btn-check:focus + .btn-outline-info, .btn-outline-info:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(28, 22, 175, 0.5);
  box-shadow: 0 0 0 0 rgba(28, 22, 175, 0.5);
}
.btn-check:checked + .btn-outline-info, .btn-check:active + .btn-outline-info, .btn-outline-info:active, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show {
  color: #fff;
  background-color: #1C16AF;
  border-color: #1C16AF;
}
.btn-check:checked + .btn-outline-info:focus, .btn-check:active + .btn-outline-info:focus, .btn-outline-info:active:focus, .btn-outline-info.active:focus, .btn-outline-info.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(28, 22, 175, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(28, 22, 175, 0.5);
}
.btn-outline-info:disabled, .btn-outline-info.disabled {
  color: #1C16AF;
  background-color: transparent;
}

.btn-outline-warning {
  color: #f37f29;
  border-color: #f37f29;
}
.btn-outline-warning:hover {
  color: #fff;
  background-color: #f37f29;
  border-color: #f37f29;
}
.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(243, 127, 41, 0.5);
  box-shadow: 0 0 0 0 rgba(243, 127, 41, 0.5);
}
.btn-check:checked + .btn-outline-warning, .btn-check:active + .btn-outline-warning, .btn-outline-warning:active, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show {
  color: #fff;
  background-color: #f37f29;
  border-color: #f37f29;
}
.btn-check:checked + .btn-outline-warning:focus, .btn-check:active + .btn-outline-warning:focus, .btn-outline-warning:active:focus, .btn-outline-warning.active:focus, .btn-outline-warning.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(243, 127, 41, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(243, 127, 41, 0.5);
}
.btn-outline-warning:disabled, .btn-outline-warning.disabled {
  color: #f37f29;
  background-color: transparent;
}

.btn-outline-danger {
  color: #d0021b;
  border-color: #d0021b;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #d0021b;
  border-color: #d0021b;
}
.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(208, 2, 27, 0.5);
  box-shadow: 0 0 0 0 rgba(208, 2, 27, 0.5);
}
.btn-check:checked + .btn-outline-danger, .btn-check:active + .btn-outline-danger, .btn-outline-danger:active, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show {
  color: #fff;
  background-color: #d0021b;
  border-color: #d0021b;
}
.btn-check:checked + .btn-outline-danger:focus, .btn-check:active + .btn-outline-danger:focus, .btn-outline-danger:active:focus, .btn-outline-danger.active:focus, .btn-outline-danger.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(208, 2, 27, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(208, 2, 27, 0.5);
}
.btn-outline-danger:disabled, .btn-outline-danger.disabled {
  color: #d0021b;
  background-color: transparent;
}

.btn-outline-light {
  color: #F5F2FC;
  border-color: #F5F2FC;
}
.btn-outline-light:hover {
  color: #5E5D61;
  background-color: #F5F2FC;
  border-color: #F5F2FC;
}
.btn-check:focus + .btn-outline-light, .btn-outline-light:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(245, 242, 252, 0.5);
  box-shadow: 0 0 0 0 rgba(245, 242, 252, 0.5);
}
.btn-check:checked + .btn-outline-light, .btn-check:active + .btn-outline-light, .btn-outline-light:active, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show {
  color: #5E5D61;
  background-color: #F5F2FC;
  border-color: #F5F2FC;
}
.btn-check:checked + .btn-outline-light:focus, .btn-check:active + .btn-outline-light:focus, .btn-outline-light:active:focus, .btn-outline-light.active:focus, .btn-outline-light.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(245, 242, 252, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(245, 242, 252, 0.5);
}
.btn-outline-light:disabled, .btn-outline-light.disabled {
  color: #F5F2FC;
  background-color: transparent;
}

.btn-outline-dark {
  color: #1c1c1c;
  border-color: #1c1c1c;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #1c1c1c;
  border-color: #1c1c1c;
}
.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(28, 28, 28, 0.5);
  box-shadow: 0 0 0 0 rgba(28, 28, 28, 0.5);
}
.btn-check:checked + .btn-outline-dark, .btn-check:active + .btn-outline-dark, .btn-outline-dark:active, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show {
  color: #fff;
  background-color: #1c1c1c;
  border-color: #1c1c1c;
}
.btn-check:checked + .btn-outline-dark:focus, .btn-check:active + .btn-outline-dark:focus, .btn-outline-dark:active:focus, .btn-outline-dark.active:focus, .btn-outline-dark.dropdown-toggle.show:focus {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(28, 28, 28, 0.5);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125), 0 0 0 0 rgba(28, 28, 28, 0.5);
}
.btn-outline-dark:disabled, .btn-outline-dark.disabled {
  color: #1c1c1c;
  background-color: transparent;
}

.btn-link {
  font-weight: 400;
  color: #6f42c1;
  text-decoration: none;
}
.btn-link:hover {
  color: #59359a;
  text-decoration: underline;
}
.btn-link:focus {
  text-decoration: underline;
}
.btn-link:disabled, .btn-link.disabled {
  color: #7F7F7F;
}

.btn-lg, .btn-group-lg > .btn {
  padding: 1.2rem 5rem;
  font-size: calc(1.2583rem + 0.0996vw);
  border-radius: 0.5rem;
}
@media (min-width: 1200px) {
  .btn-lg, .btn-group-lg > .btn {
    font-size: 1.333rem;
  }
}

.btn-sm, .btn-group-sm > .btn {
  padding: 0.4rem 1.2rem;
  font-size: 0.75rem;
  border-radius: 0.3rem;
}

.fade {
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  -o-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  -webkit-transition: width 0.35s ease;
  -o-transition: width 0.35s ease;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 1rem 0;
  margin: 0;
  font-size: 0.8rem;
  color: var(--gohub-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--gohub-dropdown-bg);
  background-clip: padding-box;
  border: 1px solid var(--gohub-dropdown-border-color);
  border-radius: 0.5rem;
  -webkit-box-shadow: var(--gohub-dropdown-box-shadow);
  box-shadow: var(--gohub-dropdown-box-shadow);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: 0.125rem;
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 540px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 720px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 960px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1140px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1440px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid var(--gohub-dropdown-border-color);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1rem;
  clear: both;
  font-weight: 400;
  color: var(--gohub-dropdown-link-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--gohub-dropdown-link-hover-color);
  text-decoration: none;
  background-color: var(--gohub-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--gohub-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--gohub-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--gohub-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 1rem 1rem;
  margin-bottom: 0;
  font-size: 0.75rem;
  color: #7F7F7F;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1rem;
  color: var(--gohub-dropdown-link-color);
}

.dropdown-menu-dark {
  color: #E7E4EE;
  background-color: #5E5D61;
  border-color: var(--gohub-dropdown-border-color);
}
.dropdown-menu-dark .dropdown-item {
  color: #E7E4EE;
}
.dropdown-menu-dark .dropdown-item:hover, .dropdown-menu-dark .dropdown-item:focus {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}
.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-item:active {
  color: var(--gohub-dropdown-link-active-color);
  background-color: var(--gohub-dropdown-link-active-bg);
}
.dropdown-menu-dark .dropdown-item.disabled, .dropdown-menu-dark .dropdown-item:disabled {
  color: #949494;
}
.dropdown-menu-dark .dropdown-divider {
  border-color: var(--gohub-dropdown-border-color);
}
.dropdown-menu-dark .dropdown-item-text {
  color: #E7E4EE;
}
.dropdown-menu-dark .dropdown-header {
  color: #949494;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 1.875rem;
  padding-left: 1.875rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.9rem;
  padding-left: 0.9rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 3.75rem;
  padding-left: 3.75rem;
}

.btn-group.show .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.show .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.btn-group-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: #6f42c1;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}


.nav-link.disabled {
  color: #7F7F7F;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #E7E4EE;
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #f2f2f2 #f2f2f2 #E7E4EE;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #7F7F7F;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #717075;
  background-color: #fff;
  border-color: #E7E4EE #E7E4EE #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.5rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #6f42c1;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding-top: 0.5rem;
  padding-right: 1rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: inherit;
  flex-wrap: inherit;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: 0.258575rem;
  padding-bottom: 0.258575rem;
  margin-right: 1rem;
  font-size: calc(1.2583rem + 0.0996vw);
  white-space: nowrap;
}
@media (min-width: 1200px) {
  .navbar-brand {
    font-size: 1.333rem;
  }
}
.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}

.navbar-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: calc(1.2583rem + 0.0996vw);
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  -webkit-transition: -webkit-box-shadow 0.15s ease-in-out;
  transition: -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (min-width: 1200px) {
  .navbar-toggler {
    font-size: 1.333rem;
  }
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0;
  box-shadow: 0 0 0 0;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--gohub-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 540px) {
  .navbar-expand-sm {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-sm .offcanvas-top,
.navbar-expand-sm .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-sm .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 720px) {
  .navbar-expand-md {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-md .offcanvas-top,
.navbar-expand-md .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-md .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 960px) {
  .navbar-expand-lg {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-lg .offcanvas-top,
.navbar-expand-lg .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-lg .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1140px) {
  .navbar-expand-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-xl .offcanvas-top,
.navbar-expand-xl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xl .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1440px) {
  .navbar-expand-xxl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
  .navbar-expand-xxl .offcanvas-top,
.navbar-expand-xxl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xxl .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas {
  position: inherit;
  bottom: 0;
  z-index: 1000;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  visibility: visible !important;
  background-color: transparent;
  border-right: 0;
  border-left: 0;
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.navbar-expand .offcanvas-top,
.navbar-expand .offcanvas-bottom {
  height: auto;
  border-top: 0;
  border-bottom: 0;
}
.navbar-expand .offcanvas-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-light .navbar-brand {
  color: var(--gohub-navbar-light-active-color);
}
.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus {
  color: var(--gohub-navbar-light-active-color);
}
.navbar-light .navbar-nav .nav-link {
  color: var(--gohub-navbar-light-color);
}
.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus {
  color: var(--gohub-navbar-light-hover-color);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: var(--gohub-navbar-light-disabled-color);
}
.navbar-light .navbar-nav .show > .nav-link,
.navbar-light .navbar-nav .nav-link.active {
  color: var(--gohub-navbar-light-active-color);
}
.navbar-light .navbar-toggler {
  color: var(--gohub-navbar-light-color);
  border-color: var(--gohub-navbar-light-toggler-border-color);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%239da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E");
}
.navbar-light .navbar-text {
  color: var(--gohub-navbar-light-color);
}
.navbar-light .navbar-text a,
.navbar-light .navbar-text a:hover,
.navbar-light .navbar-text a:focus {
  color: var(--gohub-navbar-light-active-color);
}

.navbar-dark .navbar-brand {
  color: var(--gohub-navbar-dark-active-color);
}
.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus {
  color: var(--gohub-navbar-dark-active-color);
}
.navbar-dark .navbar-nav .nav-link {
  color: var(--gohub-navbar-dark-color);
}
.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: var(--gohub-navbar-dark-hover-color);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: var(--gohub-navbar-dark-disabled-color);
}
.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .nav-link.active {
  color: var(--gohub-navbar-dark-active-color);
}
.navbar-dark .navbar-toggler {
  color: var(--gohub-navbar-dark-color);
  border-color: var(--gohub-navbar-dark-toggler-border-color);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%239da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E");
}
.navbar-dark .navbar-text {
  color: var(--gohub-navbar-dark-color);
}
.navbar-dark .navbar-text a,
.navbar-dark .navbar-text a:hover,
.navbar-dark .navbar-text a:focus {
  color: var(--gohub-navbar-dark-active-color);
}

.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--gohub-card-bg);
  background-clip: border-box;
  border: 1px solid var(--gohub-card-border-color);
  border-radius: 0.5rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: calc(0.5rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 3rem 3rem;
}

.card-title {
  margin-bottom: 1rem;
}

.card-subtitle {
  margin-top: -0.5rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 3rem;
}

.card-header {
  padding: 1rem 3rem;
  margin-bottom: 0;
  background-color: var(--gohub-card-cap-bg);
  border-bottom: 1px solid var(--gohub-card-border-color);
}
.card-header:first-child {
  border-radius: calc(0.5rem - 1px) calc(0.5rem - 1px) 0 0;
}

.card-footer {
  padding: 1rem 3rem;
  background-color: var(--gohub-card-cap-bg);
  border-top: 1px solid var(--gohub-card-border-color);
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.5rem - 1px) calc(0.5rem - 1px);
}

.card-header-tabs {
  margin-right: -1.5rem;
  margin-bottom: -1rem;
  margin-left: -1.5rem;
  border-bottom: 0;
}
.card-header-tabs .nav-link.active {
  background-color: var(--gohub-card-bg);
  border-bottom-color: var(--gohub-card-bg);
}

.card-header-pills {
  margin-right: -1.5rem;
  margin-left: -1.5rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1rem;
  border-radius: calc(0.5rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: calc(0.5rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
}

.card-group > .card {
  margin-bottom: 1rem;
}
@media (min-width: 540px) {
  .card-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }
  .card-group > .card {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.accordion-button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  padding: 1.5rem 2.6rem;
  font-size: 1rem;
  color: #717075;
  text-align: left;
  background-color: #fff;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: #1C16AF !important;
  background-color: #fff;
  -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.125);
  box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.125);
}
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='chevron-down' class='svg-inline--fa fa-chevron-down fa-w-14' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%231C16AF' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'%3E%3C/path%3E%3C/svg%3E");
  -webkit-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
.accordion-button::after {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
  margin-left: auto;
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='chevron-down' class='svg-inline--fa fa-chevron-down fa-w-14' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%23212240' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: 1rem;
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  -o-transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: #fff;
  outline: 0;
  -webkit-box-shadow: #F5F2FC;
  box-shadow: #F5F2FC;
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  background-color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.125);
}
.accordion-item:first-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.accordion-body {
  padding: 1.5rem 2.6rem;
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button {
  border-radius: 0;
}

.breadcrumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0 0;
  margin-bottom: 0;
  list-style: none;
  background-color: "transparent";
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #7F7F7F;
  content: var(--gohub-breadcrumb-divider, "/") /* rtl: var(--gohub-breadcrumb-divider, "/") */;
}
.breadcrumb-item.active {
  color: #7F7F7F;
}

.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  color: var(--gohub-pagination-color);
  background-color: var(--gohub-pagination-bg);
  border: 1px solid var(--gohub-pagination-border-color);
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--gohub-pagination-hover-color);
  text-decoration: none;
  background-color: var(--gohub-pagination-hover-bg);
  border-color: var(--gohub-pagination-hover-border-color);
}
.page-link:focus {
  z-index: 3;
  color: var(--gohub-pagination-focus-color);
  background-color: var(--gohub-pagination-focus-bg);
  outline: 0;
  -webkit-box-shadow: var(--gohub-pagination-focus-box-shadow);
  box-shadow: var(--gohub-pagination-focus-box-shadow);
}

.page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.page-item.active .page-link {
  z-index: 3;
  color: var(--gohub-pagination-active-color);
  background-color: var(--gohub-pagination-active-bg);
  border-color: var(--gohub-pagination-active-border-color);
}
.page-item.disabled .page-link {
  color: var(--gohub-pagination-disabled-color);
  pointer-events: none;
  background-color: var(--gohub-pagination-disabled-bg);
  border-color: var(--gohub-pagination-disabled-border-color);
}

.page-link {
  padding: 0.5rem 0.75rem;
}

.page-item:first-child .page-link {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: calc(1.2583rem + 0.0996vw);
}
@media (min-width: 1200px) {
  .pagination-lg .page-link {
    font-size: 1.333rem;
  }
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.7rem;
  border-bottom-left-radius: 0.7rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.7rem;
  border-bottom-right-radius: 0.7rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: 900;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.5rem;
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #f2f2f2;
  border-radius: 0.5rem;
  -webkit-box-shadow: var(--gohub-box-shadow-inset);
  box-shadow: var(--gohub-box-shadow-inset);
}

.progress-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #6f42c1;
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

.progress-bar-striped {
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  -webkit-animation: 1s linear infinite progress-bar-stripes;
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}

.btn-close {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  width: 1.2em;
  height: 1.2em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1.2em auto no-repeat;
  border: 0;
  border-radius: 0.5rem;
  opacity: 0.5;
}
.btn-close:hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}
.btn-close:focus {
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
  opacity: 1;
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.25;
}

.btn-close-white {
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
  filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
  width: 350px;
  max-width: 100%;
  font-size: 0.875rem;
  pointer-events: auto;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: var(--gohub-box-shadow);
  box-shadow: var(--gohub-box-shadow);
  border-radius: 0.5rem;
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.toast-container {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: 1rem;
}

.toast-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #7F7F7F;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: calc(0.5rem - 1px);
}
.toast-header .btn-close {
  margin-right: -0.375rem;
  margin-left: 0.75rem;
}

.toast-body {
  padding: 0.75rem;
  word-wrap: break-word;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  -o-transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  -ms-transform: translate(0, -50px);
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.modal.show .modal-dialog {
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.modal.modal-static .modal-dialog {
  -webkit-transform: scale(1.02);
  -ms-transform: scale(1.02);
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-content {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--gohub-modal-content-bg);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.7rem;
  -webkit-box-shadow: var(--gohub-box-shadow-sm);
  box-shadow: var(--gohub-box-shadow-sm);
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #1c1c1c;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.9;
}

.modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid var(--gohub-border-color);
  border-top-left-radius: calc(0.7rem - 1px);
  border-top-right-radius: calc(0.7rem - 1px);
}
.modal-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.45;
}

.modal-body {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid var(--gohub-border-color);
  border-bottom-right-radius: calc(0.7rem - 1px);
  border-bottom-left-radius: calc(0.7rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}

@media (min-width: 540px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  .modal-dialog-scrollable {
    height: calc(100% - 3.5rem);
  }

  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }

  .modal-content {
    -webkit-box-shadow: var(--gohub-box-shadow);
    box-shadow: var(--gohub-box-shadow);
  }

  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 960px) {
  .modal-lg,
.modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1140px) {
  .modal-xl {
    max-width: 1140px;
  }
}
@media (max-width: 539.98px) {
  .modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 719.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 959.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1139.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1439.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
}
.modal-fullscreen-xxl-down {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen-xxl-down .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen-xxl-down .modal-header {
  border-radius: 0;
}
.modal-fullscreen-xxl-down .modal-body {
  overflow-y: auto;
}
.modal-fullscreen-xxl-down .modal-footer {
  border-radius: 0;
}

.tooltip {
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.45;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.75rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .tooltip-arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[data-popper-placement^=top] {
  padding: 0.4rem 0;
}
.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.bs-tooltip-end, .bs-tooltip-auto[data-popper-placement^=right] {
  padding: 0 0.4rem;
}
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

.bs-tooltip-bottom, .bs-tooltip-auto[data-popper-placement^=bottom] {
  padding: 0.4rem 0;
}
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.bs-tooltip-start, .bs-tooltip-auto[data-popper-placement^=left] {
  padding: 0 0.4rem;
}
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.5rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.5rem;
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: -webkit-transform 0.8s ease-in-out;
  transition: -webkit-transform 0.8s ease-in-out;
  -o-transition: transform 0.8s ease-in-out;
  transition: transform 0.8s ease-in-out;
  transition: transform 0.8s ease-in-out, -webkit-transform 0.8s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

/* rtl:begin:ignore */
.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}

/* rtl:end:ignore */
.carousel-fade .carousel-item {
  opacity: 0;
  -webkit-transition-property: opacity;
  -o-transition-property: opacity;
  transition-property: opacity;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  -webkit-transition: opacity 0s 0.8s;
  -o-transition: opacity 0s 0.8s;
  transition: opacity 0s 0.8s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  -webkit-transition: opacity 0.15s ease;
  -o-transition: opacity 0.15s ease;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-bs-target] {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  -webkit-box-flex: 0;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  -webkit-transition: opacity 0.6s ease;
  -o-transition: opacity 0.6s ease;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  -webkit-filter: invert(1) grayscale(100);
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}

@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg) /* rtl:ignore */;
    transform: rotate(360deg) /* rtl:ignore */;
  }
}

@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg) /* rtl:ignore */;
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: 0.75s linear infinite spinner-border;
  animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
  width: 1.35rem;
  height: 1.35rem;
  border-width: 0.2em;
}

@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: 0.75s linear infinite spinner-grow;
  animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
  width: 1.35rem;
  height: 1.35rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
.spinner-grow {
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }
}
.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: 1045;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 100%;
  visibility: hidden;
  background-color: var(--gohub-modal-content-bg);
  background-clip: padding-box;
  outline: 0;
  -webkit-box-shadow: var(--gohub-box-shadow-sm);
  box-shadow: var(--gohub-box-shadow-sm);
  -webkit-transition: -webkit-transform 0.7s ease-in-out;
  transition: -webkit-transform 0.7s ease-in-out;
  -o-transition: transform 0.7s ease-in-out;
  transition: transform 0.7s ease-in-out;
  transition: transform 0.7s ease-in-out, -webkit-transform 0.7s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #1c1c1c;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.9;
}

.offcanvas-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
}
.offcanvas-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin-top: -0.5rem;
  margin-right: -0.5rem;
  margin-bottom: -0.5rem;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.45;
}

.offcanvas-body {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  padding: 1rem 1rem;
  overflow-y: auto;
}

.offcanvas-start {
  top: 0;
  left: 0;
  width: 400px;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}

.offcanvas-end {
  top: 0;
  right: 0;
  width: 400px;
  border-left: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}

.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%);
}

.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-transform: translateY(100%);
  -ms-transform: translateY(100%);
  transform: translateY(100%);
}

.offcanvas.show {
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--gohub-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --gohub-aspect-ratio: 100%;
}

.ratio-4x3 {
  --gohub-aspect-ratio: calc(3 / 4 * 100%);
}

.ratio-16x9 {
  --gohub-aspect-ratio: calc(9 / 16 * 100%);
}

.ratio-21x9 {
  --gohub-aspect-ratio: calc(9 / 21 * 100%);
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1015;
}

@media (min-width: 540px) {
  .sticky-sm-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1015;
  }
}
@media (min-width: 720px) {
  .sticky-md-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1015;
  }
}
@media (min-width: 960px) {
  .sticky-lg-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1015;
  }
}
@media (min-width: 1140px) {
  .sticky-xl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1015;
  }
}
@media (min-width: 1440px) {
  .sticky-xxl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1015;
  }
}
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-85 {
  opacity: 0.85 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.d-inline-flex {
  display: -webkit-inline-box !important;
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  -webkit-box-shadow: var(--gohub-box-shadow) !important;
  box-shadow: var(--gohub-box-shadow) !important;
}

.shadow-sm {
  -webkit-box-shadow: var(--gohub-box-shadow-sm) !important;
  box-shadow: var(--gohub-box-shadow-sm) !important;
}

.shadow-lg {
  -webkit-box-shadow: var(--gohub-box-shadow-lg) !important;
  box-shadow: var(--gohub-box-shadow-lg) !important;
}

.shadow-none {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-absolute {
  position: absolute !important;
}

.position-relative {
  position: relative !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  -webkit-transform: translateX(-50%) translateY(-50%) !important;
  -ms-transform: translateX(-50%) translateY(-50%) !important;
  transform: translateX(-50%) translateY(-50%) !important;
}

.translate-middle-x {
  -webkit-transform: translateX(-50%) !important;
  -ms-transform: translateX(-50%) !important;
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  -webkit-transform: translateY(-50%) !important;
  -ms-transform: translateY(-50%) !important;
  transform: translateY(-50%) !important;
}

.border {
  border: 1px solid var(--gohub-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid var(--gohub-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: 1px solid var(--gohub-border-color) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: 1px solid var(--gohub-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: 1px solid var(--gohub-border-color) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-facebook {
  border-color: #3c5a99 !important;
}

.border-google-plus {
  border-color: #dd4b39 !important;
}

.border-twitter {
  border-color: #1da1f2 !important;
}

.border-linkedin {
  border-color: #0077b5 !important;
}

.border-youtube {
  border-color: #ff0001 !important;
}

.border-github {
  border-color: #333333 !important;
}

.border-black {
  border-color: #000 !important;
}

.border-100 {
  border-color: #F5F2FC !important;
}

.border-200 {
  border-color: #f2f2f2 !important;
}

.border-300 {
  border-color: #E7E4EE !important;
}

.border-400 {
  border-color: #bebebe !important;
}

.border-500 {
  border-color: #949494 !important;
}

.border-600 {
  border-color: #7F7F7F !important;
}

.border-700 {
  border-color: #717075 !important;
}

.border-800 {
  border-color: #5E5D61 !important;
}

.border-900 {
  border-color: #403F42 !important;
}

.border-1000 {
  border-color: #212240 !important;
}

.border-1100 {
  border-color: #1c1c1c !important;
}

.border-white {
  border-color: #fff !important;
}

.border-primary {
  border-color: #6f42c1 !important;
}

.border-secondary {
  border-color: #D032D0 !important;
}

.border-success {
  border-color: #7ed321 !important;
}

.border-info {
  border-color: #1C16AF !important;
}

.border-warning {
  border-color: #f37f29 !important;
}

.border-danger {
  border-color: #d0021b !important;
}

.border-light {
  border-color: #F5F2FC !important;
}

.border-dark {
  border-color: #1c1c1c !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-25 {
  width: 25vw !important;
}

.vw-50 {
  width: 50vw !important;
}

.vw-75 {
  width: 75vw !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-25 {
  height: 25vh !important;
}

.vh-50 {
  height: 50vh !important;
}

.vh-75 {
  height: 75vh !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-25 {
  min-height: 25vh !important;
}

.min-vh-50 {
  min-height: 50vh !important;
}

.min-vh-75 {
  min-height: 75vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  -webkit-box-flex: 1 !important;
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
}

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
  -ms-flex-direction: row !important;
  flex-direction: row !important;
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  -webkit-box-flex: 0 !important;
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}

.flex-grow-1 {
  -webkit-box-flex: 1 !important;
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.8rem !important;
}

.gap-5 {
  gap: 3rem !important;
}

.gap-6 {
  gap: 4rem !important;
}

.gap-7 {
  gap: 5rem !important;
}

.gap-8 {
  gap: 7.5rem !important;
}

.gap-9 {
  gap: 10rem !important;
}

.gap-10 {
  gap: 12.5rem !important;
}

.gap-11 {
  gap: 15rem !important;
}

.justify-content-start {
  -webkit-box-pack: start !important;
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}

.justify-content-end {
  -webkit-box-pack: end !important;
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}

.justify-content-center {
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.justify-content-between {
  -webkit-box-pack: justify !important;
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}

.justify-content-evenly {
  -webkit-box-pack: space-evenly !important;
  -ms-flex-pack: space-evenly !important;
  justify-content: space-evenly !important;
}

.align-items-start {
  -webkit-box-align: start !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -webkit-box-align: end !important;
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -webkit-box-align: baseline !important;
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.align-items-stretch {
  -webkit-box-align: stretch !important;
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}

.align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}

.align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}

.align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}

.align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}

.align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}

.align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}

.order-first {
  -webkit-box-ordinal-group: 0 !important;
  -ms-flex-order: -1 !important;
  order: -1 !important;
}

.order-0 {
  -webkit-box-ordinal-group: 1 !important;
  -ms-flex-order: 0 !important;
  order: 0 !important;
}

.order-1 {
  -webkit-box-ordinal-group: 2 !important;
  -ms-flex-order: 1 !important;
  order: 1 !important;
}

.order-2 {
  -webkit-box-ordinal-group: 3 !important;
  -ms-flex-order: 2 !important;
  order: 2 !important;
}

.order-3 {
  -webkit-box-ordinal-group: 4 !important;
  -ms-flex-order: 3 !important;
  order: 3 !important;
}

.order-4 {
  -webkit-box-ordinal-group: 5 !important;
  -ms-flex-order: 4 !important;
  order: 4 !important;
}

.order-5 {
  -webkit-box-ordinal-group: 6 !important;
  -ms-flex-order: 5 !important;
  order: 5 !important;
}

.order-last {
  -webkit-box-ordinal-group: 7 !important;
  -ms-flex-order: 6 !important;
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.8rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.m-6 {
  margin: 4rem !important;
}

.m-7 {
  margin: 5rem !important;
}

.m-8 {
  margin: 7.5rem !important;
}

.m-9 {
  margin: 10rem !important;
}

.m-10 {
  margin: 12.5rem !important;
}

.m-11 {
  margin: 15rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.8rem !important;
  margin-left: 1.8rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-6 {
  margin-right: 4rem !important;
  margin-left: 4rem !important;
}

.mx-7 {
  margin-right: 5rem !important;
  margin-left: 5rem !important;
}

.mx-8 {
  margin-right: 7.5rem !important;
  margin-left: 7.5rem !important;
}

.mx-9 {
  margin-right: 10rem !important;
  margin-left: 10rem !important;
}

.mx-10 {
  margin-right: 12.5rem !important;
  margin-left: 12.5rem !important;
}

.mx-11 {
  margin-right: 15rem !important;
  margin-left: 15rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-4 {
  margin-top: 1.8rem !important;
  margin-bottom: 1.8rem !important;
}

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-6 {
  margin-top: 4rem !important;
  margin-bottom: 4rem !important;
}

.my-7 {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important;
}

.my-8 {
  margin-top: 7.5rem !important;
  margin-bottom: 7.5rem !important;
}

.my-9 {
  margin-top: 10rem !important;
  margin-bottom: 10rem !important;
}

.my-10 {
  margin-top: 12.5rem !important;
  margin-bottom: 12.5rem !important;
}

.my-11 {
  margin-top: 15rem !important;
  margin-bottom: 15rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.8rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.mt-6 {
  margin-top: 4rem !important;
}

.mt-7 {
  margin-top: 5rem !important;
}

.mt-8 {
  margin-top: 7.5rem !important;
}

.mt-9 {
  margin-top: 10rem !important;
}

.mt-10 {
  margin-top: 12.5rem !important;
}

.mt-11 {
  margin-top: 15rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.8rem !important;
}

.me-5 {
  margin-right: 3rem !important;
}

.me-6 {
  margin-right: 4rem !important;
}

.me-7 {
  margin-right: 5rem !important;
}

.me-8 {
  margin-right: 7.5rem !important;
}

.me-9 {
  margin-right: 10rem !important;
}

.me-10 {
  margin-right: 12.5rem !important;
}

.me-11 {
  margin-right: 15rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.8rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.mb-6 {
  margin-bottom: 4rem !important;
}

.mb-7 {
  margin-bottom: 5rem !important;
}

.mb-8 {
  margin-bottom: 7.5rem !important;
}

.mb-9 {
  margin-bottom: 10rem !important;
}

.mb-10 {
  margin-bottom: 12.5rem !important;
}

.mb-11 {
  margin-bottom: 15rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.ms-4 {
  margin-left: 1.8rem !important;
}

.ms-5 {
  margin-left: 3rem !important;
}

.ms-6 {
  margin-left: 4rem !important;
}

.ms-7 {
  margin-left: 5rem !important;
}

.ms-8 {
  margin-left: 7.5rem !important;
}

.ms-9 {
  margin-left: 10rem !important;
}

.ms-10 {
  margin-left: 12.5rem !important;
}

.ms-11 {
  margin-left: 15rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.m-n4 {
  margin: -1.8rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.m-n6 {
  margin: -4rem !important;
}

.m-n7 {
  margin: -5rem !important;
}

.m-n8 {
  margin: -7.5rem !important;
}

.m-n9 {
  margin: -10rem !important;
}

.m-n10 {
  margin: -12.5rem !important;
}

.m-n11 {
  margin: -15rem !important;
}

.mx-n1 {
  margin-right: -0.25rem !important;
  margin-left: -0.25rem !important;
}

.mx-n2 {
  margin-right: -0.5rem !important;
  margin-left: -0.5rem !important;
}

.mx-n3 {
  margin-right: -1rem !important;
  margin-left: -1rem !important;
}

.mx-n4 {
  margin-right: -1.8rem !important;
  margin-left: -1.8rem !important;
}

.mx-n5 {
  margin-right: -3rem !important;
  margin-left: -3rem !important;
}

.mx-n6 {
  margin-right: -4rem !important;
  margin-left: -4rem !important;
}

.mx-n7 {
  margin-right: -5rem !important;
  margin-left: -5rem !important;
}

.mx-n8 {
  margin-right: -7.5rem !important;
  margin-left: -7.5rem !important;
}

.mx-n9 {
  margin-right: -10rem !important;
  margin-left: -10rem !important;
}

.mx-n10 {
  margin-right: -12.5rem !important;
  margin-left: -12.5rem !important;
}

.mx-n11 {
  margin-right: -15rem !important;
  margin-left: -15rem !important;
}

.my-n1 {
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}

.my-n2 {
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}

.my-n3 {
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}

.my-n4 {
  margin-top: -1.8rem !important;
  margin-bottom: -1.8rem !important;
}

.my-n5 {
  margin-top: -3rem !important;
  margin-bottom: -3rem !important;
}

.my-n6 {
  margin-top: -4rem !important;
  margin-bottom: -4rem !important;
}

.my-n7 {
  margin-top: -5rem !important;
  margin-bottom: -5rem !important;
}

.my-n8 {
  margin-top: -7.5rem !important;
  margin-bottom: -7.5rem !important;
}

.my-n9 {
  margin-top: -10rem !important;
  margin-bottom: -10rem !important;
}

.my-n10 {
  margin-top: -12.5rem !important;
  margin-bottom: -12.5rem !important;
}

.my-n11 {
  margin-top: -15rem !important;
  margin-bottom: -15rem !important;
}

.mt-n1 {
  margin-top: -0.25rem !important;
}

.mt-n2 {
  margin-top: -0.5rem !important;
}

.mt-n3 {
  margin-top: -1rem !important;
}

.mt-n4 {
  margin-top: -1.8rem !important;
}

.mt-n5 {
  margin-top: -3rem !important;
}

.mt-n6 {
  margin-top: -4rem !important;
}

.mt-n7 {
  margin-top: -5rem !important;
}

.mt-n8 {
  margin-top: -7.5rem !important;
}

.mt-n9 {
  margin-top: -10rem !important;
}

.mt-n10 {
  margin-top: -12.5rem !important;
}

.mt-n11 {
  margin-top: -15rem !important;
}

.me-n1 {
  margin-right: -0.25rem !important;
}

.me-n2 {
  margin-right: -0.5rem !important;
}

.me-n3 {
  margin-right: -1rem !important;
}

.me-n4 {
  margin-right: -1.8rem !important;
}

.me-n5 {
  margin-right: -3rem !important;
}

.me-n6 {
  margin-right: -4rem !important;
}

.me-n7 {
  margin-right: -5rem !important;
}

.me-n8 {
  margin-right: -7.5rem !important;
}

.me-n9 {
  margin-right: -10rem !important;
}

.me-n10 {
  margin-right: -12.5rem !important;
}

.me-n11 {
  margin-right: -15rem !important;
}

.mb-n1 {
  margin-bottom: -0.25rem !important;
}

.mb-n2 {
  margin-bottom: -0.5rem !important;
}

.mb-n3 {
  margin-bottom: -1rem !important;
}

.mb-n4 {
  margin-bottom: -1.8rem !important;
}

.mb-n5 {
  margin-bottom: -3rem !important;
}

.mb-n6 {
  margin-bottom: -4rem !important;
}

.mb-n7 {
  margin-bottom: -5rem !important;
}

.mb-n8 {
  margin-bottom: -7.5rem !important;
}

.mb-n9 {
  margin-bottom: -10rem !important;
}

.mb-n10 {
  margin-bottom: -12.5rem !important;
}

.mb-n11 {
  margin-bottom: -15rem !important;
}

.ms-n1 {
  margin-left: -0.25rem !important;
}

.ms-n2 {
  margin-left: -0.5rem !important;
}

.ms-n3 {
  margin-left: -1rem !important;
}

.ms-n4 {
  margin-left: -1.8rem !important;
}

.ms-n5 {
  margin-left: -3rem !important;
}

.ms-n6 {
  margin-left: -4rem !important;
}

.ms-n7 {
  margin-left: -5rem !important;
}

.ms-n8 {
  margin-left: -7.5rem !important;
}

.ms-n9 {
  margin-left: -10rem !important;
}

.ms-n10 {
  margin-left: -12.5rem !important;
}

.ms-n11 {
  margin-left: -15rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.8rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.p-6 {
  padding: 4rem !important;
}

.p-7 {
  padding: 5rem !important;
}

.p-8 {
  padding: 7.5rem !important;
}

.p-9 {
  padding: 10rem !important;
}

.p-10 {
  padding: 12.5rem !important;
}

.p-11 {
  padding: 15rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.8rem !important;
  padding-left: 1.8rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.px-6 {
  padding-right: 4rem !important;
  padding-left: 4rem !important;
}

.px-7 {
  padding-right: 5rem !important;
  padding-left: 5rem !important;
}

.px-8 {
  padding-right: 7.5rem !important;
  padding-left: 7.5rem !important;
}

.px-9 {
  padding-right: 10rem !important;
  padding-left: 10rem !important;
}

.px-10 {
  padding-right: 12.5rem !important;
  padding-left: 12.5rem !important;
}

.px-11 {
  padding-right: 15rem !important;
  padding-left: 15rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.8rem !important;
  padding-bottom: 1.8rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.py-6 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}

.py-7 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.py-8 {
  padding-top: 7.5rem !important;
  padding-bottom: 7.5rem !important;
}

.py-9 {
  padding-top: 10rem !important;
  padding-bottom: 10rem !important;
}

.py-10 {
  padding-top: 12.5rem !important;
  padding-bottom: 12.5rem !important;
}

.py-11 {
  padding-top: 15rem !important;
  padding-bottom: 15rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.8rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pt-6 {
  padding-top: 4rem !important;
}

.pt-7 {
  padding-top: 5rem !important;
}

.pt-8 {
  padding-top: 7.5rem !important;
}

.pt-9 {
  padding-top: 10rem !important;
}

.pt-10 {
  padding-top: 12.5rem !important;
}

.pt-11 {
  padding-top: 15rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-1 {
  padding-right: 0.25rem !important;
}

.pe-2 {
  padding-right: 0.5rem !important;
}

.pe-3 {
  padding-right: 1rem !important;
}

.pe-4 {
  padding-right: 1.8rem !important;
}

.pe-5 {
  padding-right: 3rem !important;
}

.pe-6 {
  padding-right: 4rem !important;
}

.pe-7 {
  padding-right: 5rem !important;
}

.pe-8 {
  padding-right: 7.5rem !important;
}

.pe-9 {
  padding-right: 10rem !important;
}

.pe-10 {
  padding-right: 12.5rem !important;
}

.pe-11 {
  padding-right: 15rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.8rem !important;
}

.pb-5 {
  padding-bottom: 3rem !important;
}

.pb-6 {
  padding-bottom: 4rem !important;
}

.pb-7 {
  padding-bottom: 5rem !important;
}

.pb-8 {
  padding-bottom: 7.5rem !important;
}

.pb-9 {
  padding-bottom: 10rem !important;
}

.pb-10 {
  padding-bottom: 12.5rem !important;
}

.pb-11 {
  padding-bottom: 15rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-1 {
  padding-left: 0.25rem !important;
}

.ps-2 {
  padding-left: 0.5rem !important;
}

.ps-3 {
  padding-left: 1rem !important;
}

.ps-4 {
  padding-left: 1.8rem !important;
}

.ps-5 {
  padding-left: 3rem !important;
}

.ps-6 {
  padding-left: 4rem !important;
}

.ps-7 {
  padding-left: 5rem !important;
}

.ps-8 {
  padding-left: 7.5rem !important;
}

.ps-9 {
  padding-left: 10rem !important;
}

.ps-10 {
  padding-left: 12.5rem !important;
}

.ps-11 {
  padding-left: 15rem !important;
}

.font-monospace {
  font-family: var(--gohub-font-monospace) !important;
}

.fs--2 {
  font-size: 0.5627813555rem !important;
}

.fs--1 {
  font-size: 0.75rem !important;
}

.fs-0 {
  font-size: 1rem !important;
}

.fs-1 {
  font-size: 1.333rem !important;
}

.fs-2 {
  font-size: 1.777rem !important;
}

.fs-3 {
  font-size: 2.369rem !important;
}

.fs-4 {
  font-size: 3.157rem !important;
}

.fs-5 {
  font-size: 4.199rem !important;
}

.fs-6 {
  font-size: 5.584rem !important;
}

.fs-7 {
  font-size: 7.427rem !important;
}

.fs-8 {
  font-size: 9.878rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-thin {
  font-weight: 100 !important;
}

.fw-lighter {
  font-weight: 200 !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semi-bold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: 800 !important;
}

.fw-black {
  font-weight: 900 !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.45 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-primary-rgb), var(--gohub-text-opacity)) !important;
}

.text-secondary {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-secondary-rgb), var(--gohub-text-opacity)) !important;
}

.text-success {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-success-rgb), var(--gohub-text-opacity)) !important;
}

.text-info {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-info-rgb), var(--gohub-text-opacity)) !important;
}

.text-warning {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-warning-rgb), var(--gohub-text-opacity)) !important;
}

.text-danger {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-danger-rgb), var(--gohub-text-opacity)) !important;
}

.text-light {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-light-rgb), var(--gohub-text-opacity)) !important;
}

.text-dark {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-dark-rgb), var(--gohub-text-opacity)) !important;
}

.text-black {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-black-rgb), var(--gohub-text-opacity)) !important;
}

.text-white {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-white-rgb), var(--gohub-text-opacity)) !important;
}

.text-body {
  --gohub-text-opacity: 1;
  color: rgba(var(--gohub-body-color-rgb), var(--gohub-text-opacity)) !important;
}

.text-muted {
  --gohub-text-opacity: 1;
  color: #949494 !important;
}

.text-black-50 {
  --gohub-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  --gohub-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-reset {
  --gohub-text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --gohub-text-opacity: 0.25;
}

.text-opacity-50 {
  --gohub-text-opacity: 0.5;
}

.text-opacity-75 {
  --gohub-text-opacity: 0.75;
}

.text-opacity-100 {
  --gohub-text-opacity: 1;
}

.bg-facebook {
  background-color: #3c5a99 !important;
}

.bg-google-plus {
  background-color: #dd4b39 !important;
}

.bg-twitter {
  background-color: #1da1f2 !important;
}

.bg-linkedin {
  background-color: #0077b5 !important;
}

.bg-youtube {
  background-color: #ff0001 !important;
}

.bg-github {
  background-color: #333333 !important;
}

.bg-black {
  background-color: #000 !important;
}

.bg-100 {
  background-color: #F5F2FC !important;
}

.bg-200 {
  background-color: #f2f2f2 !important;
}

.bg-300 {
  background-color: #E7E4EE !important;
}

.bg-400 {
  background-color: #bebebe !important;
}

.bg-500 {
  background-color: #949494 !important;
}

.bg-600 {
  background-color: #7F7F7F !important;
}

.bg-700 {
  background-color: #717075 !important;
}

.bg-800 {
  background-color: #5E5D61 !important;
}

.bg-900 {
  background-color: #403F42 !important;
}

.bg-1000 {
  background-color: #212240 !important;
}

.bg-1100 {
  background-color: #1c1c1c !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-primary {
  background-color: #6f42c1 !important;
}

.bg-secondary {
  background-color: #D032D0 !important;
}

.bg-success {
  background-color: #7ed321 !important;
}

.bg-info {
  background-color: #1C16AF !important;
}

.bg-warning {
  background-color: #f37f29 !important;
}

.bg-danger {
  background-color: #d0021b !important;
}

.bg-light {
  background-color: #F5F2FC !important;
}

.bg-dark {
  background-color: #1c1c1c !important;
}

.bg-body {
  background-color: "body" !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.bg-opacity-10 {
  --gohub-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --gohub-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --gohub-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --gohub-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --gohub-bg-opacity: 1;
}

.bg-gradient {
  background-image: var(--gohub-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  -ms-user-select: all !important;
  user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: 0.5rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: 0.3rem !important;
}

.rounded-2 {
  border-radius: 0.5rem !important;
}

.rounded-3 {
  border-radius: 0.7rem !important;
}

.rounded-4 {
  border-radius: 1rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 10rem !important;
}

.rounded-top {
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
}

.rounded-top-lg {
  border-top-left-radius: 0.7rem !important;
  border-top-right-radius: 0.7rem !important;
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-end {
  border-top-right-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}

.rounded-end-lg {
  border-top-right-radius: 0.7rem !important;
  border-bottom-right-radius: 0.7rem !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.5rem !important;
  border-bottom-left-radius: 0.5rem !important;
}

.rounded-bottom-lg {
  border-bottom-right-radius: 0.7rem !important;
  border-bottom-left-radius: 0.7rem !important;
}

.rounded-bottom-0 {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-start {
  border-bottom-left-radius: 0.5rem !important;
  border-top-left-radius: 0.5rem !important;
}

.rounded-start-lg {
  border-bottom-left-radius: 0.7rem !important;
  border-top-left-radius: 0.7rem !important;
}

.rounded-start-0 {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.text-facebook {
  color: #3c5a99 !important;
}

.text-google-plus {
  color: #dd4b39 !important;
}

.text-twitter {
  color: #1da1f2 !important;
}

.text-linkedin {
  color: #0077b5 !important;
}

.text-youtube {
  color: #ff0001 !important;
}

.text-github {
  color: #333333 !important;
}

.text-black {
  color: #000 !important;
}

.text-100 {
  color: #F5F2FC !important;
}

.text-200 {
  color: #f2f2f2 !important;
}

.text-300 {
  color: #E7E4EE !important;
}

.text-400 {
  color: #bebebe !important;
}

.text-500 {
  color: #949494 !important;
}

.text-600 {
  color: #7F7F7F !important;
}

.text-700 {
  color: #717075 !important;
}

.text-800 {
  color: #5E5D61 !important;
}

.text-900 {
  color: #403F42 !important;
}

.text-1000 {
  color: #212240 !important;
}

.text-1100 {
  color: #1c1c1c !important;
}

.text-white {
  color: #fff !important;
}

.max-vh-25 {
  max-height: 25vh !important;
}

.max-vh-50 {
  max-height: 50vh !important;
}

.max-vh-75 {
  max-height: 75vh !important;
}

.max-vh-100 {
  max-height: 100vh !important;
}

.border-x {
  border-left: 1px solid var(--gohub-border-color) !important;
  border-right: 1px solid var(--gohub-border-color) !important;
}

.border-x-0 {
  border-left: 0 !important;
  border-right: 0 !important;
}

.border-y {
  border-top: 1px solid var(--gohub-border-color) !important;
  border-bottom: 1px solid var(--gohub-border-color) !important;
}

.border-y-0 {
  border-top: 0 !important;
  border-bottom: 0 !important;
}

.border-dashed {
  border: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-top {
  border-top: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-end {
  border-right: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-start {
  border-left: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-bottom {
  border-bottom: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-x {
  border-left: 1px dashed var(--gohub-border-color) !important;
  border-right: 1px dashed var(--gohub-border-color) !important;
}

.border-dashed-y {
  border-top: 1px dashed var(--gohub-border-color) !important;
  border-bottom: 1px dashed var(--gohub-border-color) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.bg-soft-primary {
  background-color: #eee8f8 !important;
}

.bg-soft-secondary {
  background-color: #f9e6f9 !important;
}

.bg-soft-success {
  background-color: #f0fae4 !important;
}

.bg-soft-info {
  background-color: #e4e3f5 !important;
}

.bg-soft-warning {
  background-color: #fef0e5 !important;
}

.bg-soft-danger {
  background-color: #f9e1e4 !important;
}

.bg-soft-light {
  background-color: #fefdff !important;
}

.bg-soft-dark {
  background-color: #e4e4e4 !important;
}

@media (min-width: 540px) {
  .float-sm-start {
    float: left !important;
  }

  .float-sm-end {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }

  .opacity-sm-0 {
    opacity: 0 !important;
  }

  .opacity-sm-25 {
    opacity: 0.25 !important;
  }

  .opacity-sm-50 {
    opacity: 0.5 !important;
  }

  .opacity-sm-75 {
    opacity: 0.75 !important;
  }

  .opacity-sm-85 {
    opacity: 0.85 !important;
  }

  .opacity-sm-100 {
    opacity: 1 !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-grid {
    display: grid !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-sm-none {
    display: none !important;
  }

  .position-sm-static {
    position: static !important;
  }

  .position-sm-absolute {
    position: absolute !important;
  }

  .position-sm-relative {
    position: relative !important;
  }

  .position-sm-fixed {
    position: fixed !important;
  }

  .position-sm-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }

  .translate-sm-middle {
    -webkit-transform: translateX(-50%) translateY(-50%) !important;
    -ms-transform: translateX(-50%) translateY(-50%) !important;
    transform: translateX(-50%) translateY(-50%) !important;
  }

  .translate-sm-middle-x {
    -webkit-transform: translateX(-50%) !important;
    -ms-transform: translateX(-50%) !important;
    transform: translateX(-50%) !important;
  }

  .translate-sm-middle-y {
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
  }

  .border-sm {
    border: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-0 {
    border: 0 !important;
  }

  .border-sm-top {
    border-top: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-top-0 {
    border-top: 0 !important;
  }

  .border-sm-end {
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-end-0 {
    border-right: 0 !important;
  }

  .border-sm-bottom {
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-bottom-0 {
    border-bottom: 0 !important;
  }

  .border-sm-start {
    border-left: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-start-0 {
    border-left: 0 !important;
  }

  .border-sm-facebook {
    border-color: #3c5a99 !important;
  }

  .border-sm-google-plus {
    border-color: #dd4b39 !important;
  }

  .border-sm-twitter {
    border-color: #1da1f2 !important;
  }

  .border-sm-linkedin {
    border-color: #0077b5 !important;
  }

  .border-sm-youtube {
    border-color: #ff0001 !important;
  }

  .border-sm-github {
    border-color: #333333 !important;
  }

  .border-sm-black {
    border-color: #000 !important;
  }

  .border-sm-100 {
    border-color: #F5F2FC !important;
  }

  .border-sm-200 {
    border-color: #f2f2f2 !important;
  }

  .border-sm-300 {
    border-color: #E7E4EE !important;
  }

  .border-sm-400 {
    border-color: #bebebe !important;
  }

  .border-sm-500 {
    border-color: #949494 !important;
  }

  .border-sm-600 {
    border-color: #7F7F7F !important;
  }

  .border-sm-700 {
    border-color: #717075 !important;
  }

  .border-sm-800 {
    border-color: #5E5D61 !important;
  }

  .border-sm-900 {
    border-color: #403F42 !important;
  }

  .border-sm-1000 {
    border-color: #212240 !important;
  }

  .border-sm-1100 {
    border-color: #1c1c1c !important;
  }

  .border-sm-white {
    border-color: #fff !important;
  }

  .border-sm-primary {
    border-color: #6f42c1 !important;
  }

  .border-sm-secondary {
    border-color: #D032D0 !important;
  }

  .border-sm-success {
    border-color: #7ed321 !important;
  }

  .border-sm-info {
    border-color: #1C16AF !important;
  }

  .border-sm-warning {
    border-color: #f37f29 !important;
  }

  .border-sm-danger {
    border-color: #d0021b !important;
  }

  .border-sm-light {
    border-color: #F5F2FC !important;
  }

  .border-sm-dark {
    border-color: #1c1c1c !important;
  }

  .w-sm-25 {
    width: 25% !important;
  }

  .w-sm-50 {
    width: 50% !important;
  }

  .w-sm-75 {
    width: 75% !important;
  }

  .w-sm-100 {
    width: 100% !important;
  }

  .w-sm-auto {
    width: auto !important;
  }

  .vw-sm-25 {
    width: 25vw !important;
  }

  .vw-sm-50 {
    width: 50vw !important;
  }

  .vw-sm-75 {
    width: 75vw !important;
  }

  .vw-sm-100 {
    width: 100vw !important;
  }

  .h-sm-25 {
    height: 25% !important;
  }

  .h-sm-50 {
    height: 50% !important;
  }

  .h-sm-75 {
    height: 75% !important;
  }

  .h-sm-100 {
    height: 100% !important;
  }

  .h-sm-auto {
    height: auto !important;
  }

  .vh-sm-25 {
    height: 25vh !important;
  }

  .vh-sm-50 {
    height: 50vh !important;
  }

  .vh-sm-75 {
    height: 75vh !important;
  }

  .vh-sm-100 {
    height: 100vh !important;
  }

  .min-vh-sm-25 {
    min-height: 25vh !important;
  }

  .min-vh-sm-50 {
    min-height: 50vh !important;
  }

  .min-vh-sm-75 {
    min-height: 75vh !important;
  }

  .min-vh-sm-100 {
    min-height: 100vh !important;
  }

  .flex-sm-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-sm-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-sm-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-sm-grow-0 {
    -webkit-box-flex: 0 !important;
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .gap-sm-0 {
    gap: 0 !important;
  }

  .gap-sm-1 {
    gap: 0.25rem !important;
  }

  .gap-sm-2 {
    gap: 0.5rem !important;
  }

  .gap-sm-3 {
    gap: 1rem !important;
  }

  .gap-sm-4 {
    gap: 1.8rem !important;
  }

  .gap-sm-5 {
    gap: 3rem !important;
  }

  .gap-sm-6 {
    gap: 4rem !important;
  }

  .gap-sm-7 {
    gap: 5rem !important;
  }

  .gap-sm-8 {
    gap: 7.5rem !important;
  }

  .gap-sm-9 {
    gap: 10rem !important;
  }

  .gap-sm-10 {
    gap: 12.5rem !important;
  }

  .gap-sm-11 {
    gap: 15rem !important;
  }

  .justify-content-sm-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-sm-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .justify-content-sm-evenly {
    -webkit-box-pack: space-evenly !important;
    -ms-flex-pack: space-evenly !important;
    justify-content: space-evenly !important;
  }

  .align-items-sm-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-sm-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }

  .order-sm-first {
    -webkit-box-ordinal-group: 0 !important;
    -ms-flex-order: -1 !important;
    order: -1 !important;
  }

  .order-sm-0 {
    -webkit-box-ordinal-group: 1 !important;
    -ms-flex-order: 0 !important;
    order: 0 !important;
  }

  .order-sm-1 {
    -webkit-box-ordinal-group: 2 !important;
    -ms-flex-order: 1 !important;
    order: 1 !important;
  }

  .order-sm-2 {
    -webkit-box-ordinal-group: 3 !important;
    -ms-flex-order: 2 !important;
    order: 2 !important;
  }

  .order-sm-3 {
    -webkit-box-ordinal-group: 4 !important;
    -ms-flex-order: 3 !important;
    order: 3 !important;
  }

  .order-sm-4 {
    -webkit-box-ordinal-group: 5 !important;
    -ms-flex-order: 4 !important;
    order: 4 !important;
  }

  .order-sm-5 {
    -webkit-box-ordinal-group: 6 !important;
    -ms-flex-order: 5 !important;
    order: 5 !important;
  }

  .order-sm-last {
    -webkit-box-ordinal-group: 7 !important;
    -ms-flex-order: 6 !important;
    order: 6 !important;
  }

  .m-sm-0 {
    margin: 0 !important;
  }

  .m-sm-1 {
    margin: 0.25rem !important;
  }

  .m-sm-2 {
    margin: 0.5rem !important;
  }

  .m-sm-3 {
    margin: 1rem !important;
  }

  .m-sm-4 {
    margin: 1.8rem !important;
  }

  .m-sm-5 {
    margin: 3rem !important;
  }

  .m-sm-6 {
    margin: 4rem !important;
  }

  .m-sm-7 {
    margin: 5rem !important;
  }

  .m-sm-8 {
    margin: 7.5rem !important;
  }

  .m-sm-9 {
    margin: 10rem !important;
  }

  .m-sm-10 {
    margin: 12.5rem !important;
  }

  .m-sm-11 {
    margin: 15rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-sm-4 {
    margin-right: 1.8rem !important;
    margin-left: 1.8rem !important;
  }

  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-sm-6 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }

  .mx-sm-7 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-sm-8 {
    margin-right: 7.5rem !important;
    margin-left: 7.5rem !important;
  }

  .mx-sm-9 {
    margin-right: 10rem !important;
    margin-left: 10rem !important;
  }

  .mx-sm-10 {
    margin-right: 12.5rem !important;
    margin-left: 12.5rem !important;
  }

  .mx-sm-11 {
    margin-right: 15rem !important;
    margin-left: 15rem !important;
  }

  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-sm-4 {
    margin-top: 1.8rem !important;
    margin-bottom: 1.8rem !important;
  }

  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-sm-6 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }

  .my-sm-7 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-sm-8 {
    margin-top: 7.5rem !important;
    margin-bottom: 7.5rem !important;
  }

  .my-sm-9 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }

  .my-sm-10 {
    margin-top: 12.5rem !important;
    margin-bottom: 12.5rem !important;
  }

  .my-sm-11 {
    margin-top: 15rem !important;
    margin-bottom: 15rem !important;
  }

  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-sm-0 {
    margin-top: 0 !important;
  }

  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }

  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }

  .mt-sm-3 {
    margin-top: 1rem !important;
  }

  .mt-sm-4 {
    margin-top: 1.8rem !important;
  }

  .mt-sm-5 {
    margin-top: 3rem !important;
  }

  .mt-sm-6 {
    margin-top: 4rem !important;
  }

  .mt-sm-7 {
    margin-top: 5rem !important;
  }

  .mt-sm-8 {
    margin-top: 7.5rem !important;
  }

  .mt-sm-9 {
    margin-top: 10rem !important;
  }

  .mt-sm-10 {
    margin-top: 12.5rem !important;
  }

  .mt-sm-11 {
    margin-top: 15rem !important;
  }

  .mt-sm-auto {
    margin-top: auto !important;
  }

  .me-sm-0 {
    margin-right: 0 !important;
  }

  .me-sm-1 {
    margin-right: 0.25rem !important;
  }

  .me-sm-2 {
    margin-right: 0.5rem !important;
  }

  .me-sm-3 {
    margin-right: 1rem !important;
  }

  .me-sm-4 {
    margin-right: 1.8rem !important;
  }

  .me-sm-5 {
    margin-right: 3rem !important;
  }

  .me-sm-6 {
    margin-right: 4rem !important;
  }

  .me-sm-7 {
    margin-right: 5rem !important;
  }

  .me-sm-8 {
    margin-right: 7.5rem !important;
  }

  .me-sm-9 {
    margin-right: 10rem !important;
  }

  .me-sm-10 {
    margin-right: 12.5rem !important;
  }

  .me-sm-11 {
    margin-right: 15rem !important;
  }

  .me-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-0 {
    margin-bottom: 0 !important;
  }

  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }

  .mb-sm-4 {
    margin-bottom: 1.8rem !important;
  }

  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }

  .mb-sm-6 {
    margin-bottom: 4rem !important;
  }

  .mb-sm-7 {
    margin-bottom: 5rem !important;
  }

  .mb-sm-8 {
    margin-bottom: 7.5rem !important;
  }

  .mb-sm-9 {
    margin-bottom: 10rem !important;
  }

  .mb-sm-10 {
    margin-bottom: 12.5rem !important;
  }

  .mb-sm-11 {
    margin-bottom: 15rem !important;
  }

  .mb-sm-auto {
    margin-bottom: auto !important;
  }

  .ms-sm-0 {
    margin-left: 0 !important;
  }

  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }

  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }

  .ms-sm-3 {
    margin-left: 1rem !important;
  }

  .ms-sm-4 {
    margin-left: 1.8rem !important;
  }

  .ms-sm-5 {
    margin-left: 3rem !important;
  }

  .ms-sm-6 {
    margin-left: 4rem !important;
  }

  .ms-sm-7 {
    margin-left: 5rem !important;
  }

  .ms-sm-8 {
    margin-left: 7.5rem !important;
  }

  .ms-sm-9 {
    margin-left: 10rem !important;
  }

  .ms-sm-10 {
    margin-left: 12.5rem !important;
  }

  .ms-sm-11 {
    margin-left: 15rem !important;
  }

  .ms-sm-auto {
    margin-left: auto !important;
  }

  .m-sm-n1 {
    margin: -0.25rem !important;
  }

  .m-sm-n2 {
    margin: -0.5rem !important;
  }

  .m-sm-n3 {
    margin: -1rem !important;
  }

  .m-sm-n4 {
    margin: -1.8rem !important;
  }

  .m-sm-n5 {
    margin: -3rem !important;
  }

  .m-sm-n6 {
    margin: -4rem !important;
  }

  .m-sm-n7 {
    margin: -5rem !important;
  }

  .m-sm-n8 {
    margin: -7.5rem !important;
  }

  .m-sm-n9 {
    margin: -10rem !important;
  }

  .m-sm-n10 {
    margin: -12.5rem !important;
  }

  .m-sm-n11 {
    margin: -15rem !important;
  }

  .mx-sm-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }

  .mx-sm-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }

  .mx-sm-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }

  .mx-sm-n4 {
    margin-right: -1.8rem !important;
    margin-left: -1.8rem !important;
  }

  .mx-sm-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }

  .mx-sm-n6 {
    margin-right: -4rem !important;
    margin-left: -4rem !important;
  }

  .mx-sm-n7 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-sm-n8 {
    margin-right: -7.5rem !important;
    margin-left: -7.5rem !important;
  }

  .mx-sm-n9 {
    margin-right: -10rem !important;
    margin-left: -10rem !important;
  }

  .mx-sm-n10 {
    margin-right: -12.5rem !important;
    margin-left: -12.5rem !important;
  }

  .mx-sm-n11 {
    margin-right: -15rem !important;
    margin-left: -15rem !important;
  }

  .my-sm-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }

  .my-sm-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }

  .my-sm-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }

  .my-sm-n4 {
    margin-top: -1.8rem !important;
    margin-bottom: -1.8rem !important;
  }

  .my-sm-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }

  .my-sm-n6 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }

  .my-sm-n7 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-sm-n8 {
    margin-top: -7.5rem !important;
    margin-bottom: -7.5rem !important;
  }

  .my-sm-n9 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }

  .my-sm-n10 {
    margin-top: -12.5rem !important;
    margin-bottom: -12.5rem !important;
  }

  .my-sm-n11 {
    margin-top: -15rem !important;
    margin-bottom: -15rem !important;
  }

  .mt-sm-n1 {
    margin-top: -0.25rem !important;
  }

  .mt-sm-n2 {
    margin-top: -0.5rem !important;
  }

  .mt-sm-n3 {
    margin-top: -1rem !important;
  }

  .mt-sm-n4 {
    margin-top: -1.8rem !important;
  }

  .mt-sm-n5 {
    margin-top: -3rem !important;
  }

  .mt-sm-n6 {
    margin-top: -4rem !important;
  }

  .mt-sm-n7 {
    margin-top: -5rem !important;
  }

  .mt-sm-n8 {
    margin-top: -7.5rem !important;
  }

  .mt-sm-n9 {
    margin-top: -10rem !important;
  }

  .mt-sm-n10 {
    margin-top: -12.5rem !important;
  }

  .mt-sm-n11 {
    margin-top: -15rem !important;
  }

  .me-sm-n1 {
    margin-right: -0.25rem !important;
  }

  .me-sm-n2 {
    margin-right: -0.5rem !important;
  }

  .me-sm-n3 {
    margin-right: -1rem !important;
  }

  .me-sm-n4 {
    margin-right: -1.8rem !important;
  }

  .me-sm-n5 {
    margin-right: -3rem !important;
  }

  .me-sm-n6 {
    margin-right: -4rem !important;
  }

  .me-sm-n7 {
    margin-right: -5rem !important;
  }

  .me-sm-n8 {
    margin-right: -7.5rem !important;
  }

  .me-sm-n9 {
    margin-right: -10rem !important;
  }

  .me-sm-n10 {
    margin-right: -12.5rem !important;
  }

  .me-sm-n11 {
    margin-right: -15rem !important;
  }

  .mb-sm-n1 {
    margin-bottom: -0.25rem !important;
  }

  .mb-sm-n2 {
    margin-bottom: -0.5rem !important;
  }

  .mb-sm-n3 {
    margin-bottom: -1rem !important;
  }

  .mb-sm-n4 {
    margin-bottom: -1.8rem !important;
  }

  .mb-sm-n5 {
    margin-bottom: -3rem !important;
  }

  .mb-sm-n6 {
    margin-bottom: -4rem !important;
  }

  .mb-sm-n7 {
    margin-bottom: -5rem !important;
  }

  .mb-sm-n8 {
    margin-bottom: -7.5rem !important;
  }

  .mb-sm-n9 {
    margin-bottom: -10rem !important;
  }

  .mb-sm-n10 {
    margin-bottom: -12.5rem !important;
  }

  .mb-sm-n11 {
    margin-bottom: -15rem !important;
  }

  .ms-sm-n1 {
    margin-left: -0.25rem !important;
  }

  .ms-sm-n2 {
    margin-left: -0.5rem !important;
  }

  .ms-sm-n3 {
    margin-left: -1rem !important;
  }

  .ms-sm-n4 {
    margin-left: -1.8rem !important;
  }

  .ms-sm-n5 {
    margin-left: -3rem !important;
  }

  .ms-sm-n6 {
    margin-left: -4rem !important;
  }

  .ms-sm-n7 {
    margin-left: -5rem !important;
  }

  .ms-sm-n8 {
    margin-left: -7.5rem !important;
  }

  .ms-sm-n9 {
    margin-left: -10rem !important;
  }

  .ms-sm-n10 {
    margin-left: -12.5rem !important;
  }

  .ms-sm-n11 {
    margin-left: -15rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .p-sm-1 {
    padding: 0.25rem !important;
  }

  .p-sm-2 {
    padding: 0.5rem !important;
  }

  .p-sm-3 {
    padding: 1rem !important;
  }

  .p-sm-4 {
    padding: 1.8rem !important;
  }

  .p-sm-5 {
    padding: 3rem !important;
  }

  .p-sm-6 {
    padding: 4rem !important;
  }

  .p-sm-7 {
    padding: 5rem !important;
  }

  .p-sm-8 {
    padding: 7.5rem !important;
  }

  .p-sm-9 {
    padding: 10rem !important;
  }

  .p-sm-10 {
    padding: 12.5rem !important;
  }

  .p-sm-11 {
    padding: 15rem !important;
  }

  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-sm-4 {
    padding-right: 1.8rem !important;
    padding-left: 1.8rem !important;
  }

  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .px-sm-6 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }

  .px-sm-7 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-sm-8 {
    padding-right: 7.5rem !important;
    padding-left: 7.5rem !important;
  }

  .px-sm-9 {
    padding-right: 10rem !important;
    padding-left: 10rem !important;
  }

  .px-sm-10 {
    padding-right: 12.5rem !important;
    padding-left: 12.5rem !important;
  }

  .px-sm-11 {
    padding-right: 15rem !important;
    padding-left: 15rem !important;
  }

  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-sm-4 {
    padding-top: 1.8rem !important;
    padding-bottom: 1.8rem !important;
  }

  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-sm-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-sm-7 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-sm-8 {
    padding-top: 7.5rem !important;
    padding-bottom: 7.5rem !important;
  }

  .py-sm-9 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }

  .py-sm-10 {
    padding-top: 12.5rem !important;
    padding-bottom: 12.5rem !important;
  }

  .py-sm-11 {
    padding-top: 15rem !important;
    padding-bottom: 15rem !important;
  }

  .pt-sm-0 {
    padding-top: 0 !important;
  }

  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }

  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }

  .pt-sm-3 {
    padding-top: 1rem !important;
  }

  .pt-sm-4 {
    padding-top: 1.8rem !important;
  }

  .pt-sm-5 {
    padding-top: 3rem !important;
  }

  .pt-sm-6 {
    padding-top: 4rem !important;
  }

  .pt-sm-7 {
    padding-top: 5rem !important;
  }

  .pt-sm-8 {
    padding-top: 7.5rem !important;
  }

  .pt-sm-9 {
    padding-top: 10rem !important;
  }

  .pt-sm-10 {
    padding-top: 12.5rem !important;
  }

  .pt-sm-11 {
    padding-top: 15rem !important;
  }

  .pe-sm-0 {
    padding-right: 0 !important;
  }

  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }

  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }

  .pe-sm-3 {
    padding-right: 1rem !important;
  }

  .pe-sm-4 {
    padding-right: 1.8rem !important;
  }

  .pe-sm-5 {
    padding-right: 3rem !important;
  }

  .pe-sm-6 {
    padding-right: 4rem !important;
  }

  .pe-sm-7 {
    padding-right: 5rem !important;
  }

  .pe-sm-8 {
    padding-right: 7.5rem !important;
  }

  .pe-sm-9 {
    padding-right: 10rem !important;
  }

  .pe-sm-10 {
    padding-right: 12.5rem !important;
  }

  .pe-sm-11 {
    padding-right: 15rem !important;
  }

  .pb-sm-0 {
    padding-bottom: 0 !important;
  }

  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }

  .pb-sm-4 {
    padding-bottom: 1.8rem !important;
  }

  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }

  .pb-sm-6 {
    padding-bottom: 4rem !important;
  }

  .pb-sm-7 {
    padding-bottom: 5rem !important;
  }

  .pb-sm-8 {
    padding-bottom: 7.5rem !important;
  }

  .pb-sm-9 {
    padding-bottom: 10rem !important;
  }

  .pb-sm-10 {
    padding-bottom: 12.5rem !important;
  }

  .pb-sm-11 {
    padding-bottom: 15rem !important;
  }

  .ps-sm-0 {
    padding-left: 0 !important;
  }

  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }

  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }

  .ps-sm-3 {
    padding-left: 1rem !important;
  }

  .ps-sm-4 {
    padding-left: 1.8rem !important;
  }

  .ps-sm-5 {
    padding-left: 3rem !important;
  }

  .ps-sm-6 {
    padding-left: 4rem !important;
  }

  .ps-sm-7 {
    padding-left: 5rem !important;
  }

  .ps-sm-8 {
    padding-left: 7.5rem !important;
  }

  .ps-sm-9 {
    padding-left: 10rem !important;
  }

  .ps-sm-10 {
    padding-left: 12.5rem !important;
  }

  .ps-sm-11 {
    padding-left: 15rem !important;
  }

  .fs-sm--2 {
    font-size: 0.5627813555rem !important;
  }

  .fs-sm--1 {
    font-size: 0.75rem !important;
  }

  .fs-sm-0 {
    font-size: 1rem !important;
  }

  .fs-sm-1 {
    font-size: 1.333rem !important;
  }

  .fs-sm-2 {
    font-size: 1.777rem !important;
  }

  .fs-sm-3 {
    font-size: 2.369rem !important;
  }

  .fs-sm-4 {
    font-size: 3.157rem !important;
  }

  .fs-sm-5 {
    font-size: 4.199rem !important;
  }

  .fs-sm-6 {
    font-size: 5.584rem !important;
  }

  .fs-sm-7 {
    font-size: 7.427rem !important;
  }

  .fs-sm-8 {
    font-size: 9.878rem !important;
  }

  .text-sm-start {
    text-align: left !important;
  }

  .text-sm-end {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }

  .rounded-sm-top {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
  }

  .rounded-sm-top-lg {
    border-top-left-radius: 0.7rem !important;
    border-top-right-radius: 0.7rem !important;
  }

  .rounded-sm-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .rounded-sm-end {
    border-top-right-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
  }

  .rounded-sm-end-lg {
    border-top-right-radius: 0.7rem !important;
    border-bottom-right-radius: 0.7rem !important;
  }

  .rounded-sm-end-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .rounded-sm-bottom {
    border-bottom-right-radius: 0.5rem !important;
    border-bottom-left-radius: 0.5rem !important;
  }

  .rounded-sm-bottom-lg {
    border-bottom-right-radius: 0.7rem !important;
    border-bottom-left-radius: 0.7rem !important;
  }

  .rounded-sm-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .rounded-sm-start {
    border-bottom-left-radius: 0.5rem !important;
    border-top-left-radius: 0.5rem !important;
  }

  .rounded-sm-start-lg {
    border-bottom-left-radius: 0.7rem !important;
    border-top-left-radius: 0.7rem !important;
  }

  .rounded-sm-start-0 {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }

  .max-vh-sm-25 {
    max-height: 25vh !important;
  }

  .max-vh-sm-50 {
    max-height: 50vh !important;
  }

  .max-vh-sm-75 {
    max-height: 75vh !important;
  }

  .max-vh-sm-100 {
    max-height: 100vh !important;
  }

  .border-sm-x {
    border-left: 1px solid var(--gohub-border-color) !important;
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
  }

  .border-sm-y {
    border-top: 1px solid var(--gohub-border-color) !important;
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-sm-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  .border-sm-dashed {
    border: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-top {
    border-top: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-end {
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-start {
    border-left: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-bottom {
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-x {
    border-left: 1px dashed var(--gohub-border-color) !important;
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-sm-dashed-y {
    border-top: 1px dashed var(--gohub-border-color) !important;
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .rounded-sm-0 {
    border-radius: 0 !important;
  }
}
@media (min-width: 720px) {
  .float-md-start {
    float: left !important;
  }

  .float-md-end {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }

  .opacity-md-0 {
    opacity: 0 !important;
  }

  .opacity-md-25 {
    opacity: 0.25 !important;
  }

  .opacity-md-50 {
    opacity: 0.5 !important;
  }

  .opacity-md-75 {
    opacity: 0.75 !important;
  }

  .opacity-md-85 {
    opacity: 0.85 !important;
  }

  .opacity-md-100 {
    opacity: 1 !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-grid {
    display: grid !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-md-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-md-none {
    display: none !important;
  }

  .position-md-static {
    position: static !important;
  }

  .position-md-absolute {
    position: absolute !important;
  }

  .position-md-relative {
    position: relative !important;
  }

  .position-md-fixed {
    position: fixed !important;
  }

  .position-md-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }

  .translate-md-middle {
    -webkit-transform: translateX(-50%) translateY(-50%) !important;
    -ms-transform: translateX(-50%) translateY(-50%) !important;
    transform: translateX(-50%) translateY(-50%) !important;
  }

  .translate-md-middle-x {
    -webkit-transform: translateX(-50%) !important;
    -ms-transform: translateX(-50%) !important;
    transform: translateX(-50%) !important;
  }

  .translate-md-middle-y {
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
  }

  .border-md {
    border: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-0 {
    border: 0 !important;
  }

  .border-md-top {
    border-top: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-top-0 {
    border-top: 0 !important;
  }

  .border-md-end {
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-end-0 {
    border-right: 0 !important;
  }

  .border-md-bottom {
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-bottom-0 {
    border-bottom: 0 !important;
  }

  .border-md-start {
    border-left: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-start-0 {
    border-left: 0 !important;
  }

  .border-md-facebook {
    border-color: #3c5a99 !important;
  }

  .border-md-google-plus {
    border-color: #dd4b39 !important;
  }

  .border-md-twitter {
    border-color: #1da1f2 !important;
  }

  .border-md-linkedin {
    border-color: #0077b5 !important;
  }

  .border-md-youtube {
    border-color: #ff0001 !important;
  }

  .border-md-github {
    border-color: #333333 !important;
  }

  .border-md-black {
    border-color: #000 !important;
  }

  .border-md-100 {
    border-color: #F5F2FC !important;
  }

  .border-md-200 {
    border-color: #f2f2f2 !important;
  }

  .border-md-300 {
    border-color: #E7E4EE !important;
  }

  .border-md-400 {
    border-color: #bebebe !important;
  }

  .border-md-500 {
    border-color: #949494 !important;
  }

  .border-md-600 {
    border-color: #7F7F7F !important;
  }

  .border-md-700 {
    border-color: #717075 !important;
  }

  .border-md-800 {
    border-color: #5E5D61 !important;
  }

  .border-md-900 {
    border-color: #403F42 !important;
  }

  .border-md-1000 {
    border-color: #212240 !important;
  }

  .border-md-1100 {
    border-color: #1c1c1c !important;
  }

  .border-md-white {
    border-color: #fff !important;
  }

  .border-md-primary {
    border-color: #6f42c1 !important;
  }

  .border-md-secondary {
    border-color: #D032D0 !important;
  }

  .border-md-success {
    border-color: #7ed321 !important;
  }

  .border-md-info {
    border-color: #1C16AF !important;
  }

  .border-md-warning {
    border-color: #f37f29 !important;
  }

  .border-md-danger {
    border-color: #d0021b !important;
  }

  .border-md-light {
    border-color: #F5F2FC !important;
  }

  .border-md-dark {
    border-color: #1c1c1c !important;
  }

  .w-md-25 {
    width: 25% !important;
  }

  .w-md-50 {
    width: 50% !important;
  }

  .w-md-75 {
    width: 75% !important;
  }

  .w-md-100 {
    width: 100% !important;
  }

  .w-md-auto {
    width: auto !important;
  }

  .vw-md-25 {
    width: 25vw !important;
  }

  .vw-md-50 {
    width: 50vw !important;
  }

  .vw-md-75 {
    width: 75vw !important;
  }

  .vw-md-100 {
    width: 100vw !important;
  }

  .h-md-25 {
    height: 25% !important;
  }

  .h-md-50 {
    height: 50% !important;
  }

  .h-md-75 {
    height: 75% !important;
  }

  .h-md-100 {
    height: 100% !important;
  }

  .h-md-auto {
    height: auto !important;
  }

  .vh-md-25 {
    height: 25vh !important;
  }

  .vh-md-50 {
    height: 50vh !important;
  }

  .vh-md-75 {
    height: 75vh !important;
  }

  .vh-md-100 {
    height: 100vh !important;
  }

  .min-vh-md-25 {
    min-height: 25vh !important;
  }

  .min-vh-md-50 {
    min-height: 50vh !important;
  }

  .min-vh-md-75 {
    min-height: 75vh !important;
  }

  .min-vh-md-100 {
    min-height: 100vh !important;
  }

  .flex-md-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-md-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-md-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-md-grow-0 {
    -webkit-box-flex: 0 !important;
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .gap-md-0 {
    gap: 0 !important;
  }

  .gap-md-1 {
    gap: 0.25rem !important;
  }

  .gap-md-2 {
    gap: 0.5rem !important;
  }

  .gap-md-3 {
    gap: 1rem !important;
  }

  .gap-md-4 {
    gap: 1.8rem !important;
  }

  .gap-md-5 {
    gap: 3rem !important;
  }

  .gap-md-6 {
    gap: 4rem !important;
  }

  .gap-md-7 {
    gap: 5rem !important;
  }

  .gap-md-8 {
    gap: 7.5rem !important;
  }

  .gap-md-9 {
    gap: 10rem !important;
  }

  .gap-md-10 {
    gap: 12.5rem !important;
  }

  .gap-md-11 {
    gap: 15rem !important;
  }

  .justify-content-md-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-md-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .justify-content-md-evenly {
    -webkit-box-pack: space-evenly !important;
    -ms-flex-pack: space-evenly !important;
    justify-content: space-evenly !important;
  }

  .align-items-md-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-md-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-md-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-md-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }

  .order-md-first {
    -webkit-box-ordinal-group: 0 !important;
    -ms-flex-order: -1 !important;
    order: -1 !important;
  }

  .order-md-0 {
    -webkit-box-ordinal-group: 1 !important;
    -ms-flex-order: 0 !important;
    order: 0 !important;
  }

  .order-md-1 {
    -webkit-box-ordinal-group: 2 !important;
    -ms-flex-order: 1 !important;
    order: 1 !important;
  }

  .order-md-2 {
    -webkit-box-ordinal-group: 3 !important;
    -ms-flex-order: 2 !important;
    order: 2 !important;
  }

  .order-md-3 {
    -webkit-box-ordinal-group: 4 !important;
    -ms-flex-order: 3 !important;
    order: 3 !important;
  }

  .order-md-4 {
    -webkit-box-ordinal-group: 5 !important;
    -ms-flex-order: 4 !important;
    order: 4 !important;
  }

  .order-md-5 {
    -webkit-box-ordinal-group: 6 !important;
    -ms-flex-order: 5 !important;
    order: 5 !important;
  }

  .order-md-last {
    -webkit-box-ordinal-group: 7 !important;
    -ms-flex-order: 6 !important;
    order: 6 !important;
  }

  .m-md-0 {
    margin: 0 !important;
  }

  .m-md-1 {
    margin: 0.25rem !important;
  }

  .m-md-2 {
    margin: 0.5rem !important;
  }

  .m-md-3 {
    margin: 1rem !important;
  }

  .m-md-4 {
    margin: 1.8rem !important;
  }

  .m-md-5 {
    margin: 3rem !important;
  }

  .m-md-6 {
    margin: 4rem !important;
  }

  .m-md-7 {
    margin: 5rem !important;
  }

  .m-md-8 {
    margin: 7.5rem !important;
  }

  .m-md-9 {
    margin: 10rem !important;
  }

  .m-md-10 {
    margin: 12.5rem !important;
  }

  .m-md-11 {
    margin: 15rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-md-4 {
    margin-right: 1.8rem !important;
    margin-left: 1.8rem !important;
  }

  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-md-6 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }

  .mx-md-7 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-md-8 {
    margin-right: 7.5rem !important;
    margin-left: 7.5rem !important;
  }

  .mx-md-9 {
    margin-right: 10rem !important;
    margin-left: 10rem !important;
  }

  .mx-md-10 {
    margin-right: 12.5rem !important;
    margin-left: 12.5rem !important;
  }

  .mx-md-11 {
    margin-right: 15rem !important;
    margin-left: 15rem !important;
  }

  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-md-4 {
    margin-top: 1.8rem !important;
    margin-bottom: 1.8rem !important;
  }

  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-md-6 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }

  .my-md-7 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-md-8 {
    margin-top: 7.5rem !important;
    margin-bottom: 7.5rem !important;
  }

  .my-md-9 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }

  .my-md-10 {
    margin-top: 12.5rem !important;
    margin-bottom: 12.5rem !important;
  }

  .my-md-11 {
    margin-top: 15rem !important;
    margin-bottom: 15rem !important;
  }

  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-md-0 {
    margin-top: 0 !important;
  }

  .mt-md-1 {
    margin-top: 0.25rem !important;
  }

  .mt-md-2 {
    margin-top: 0.5rem !important;
  }

  .mt-md-3 {
    margin-top: 1rem !important;
  }

  .mt-md-4 {
    margin-top: 1.8rem !important;
  }

  .mt-md-5 {
    margin-top: 3rem !important;
  }

  .mt-md-6 {
    margin-top: 4rem !important;
  }

  .mt-md-7 {
    margin-top: 5rem !important;
  }

  .mt-md-8 {
    margin-top: 7.5rem !important;
  }

  .mt-md-9 {
    margin-top: 10rem !important;
  }

  .mt-md-10 {
    margin-top: 12.5rem !important;
  }

  .mt-md-11 {
    margin-top: 15rem !important;
  }

  .mt-md-auto {
    margin-top: auto !important;
  }

  .me-md-0 {
    margin-right: 0 !important;
  }

  .me-md-1 {
    margin-right: 0.25rem !important;
  }

  .me-md-2 {
    margin-right: 0.5rem !important;
  }

  .me-md-3 {
    margin-right: 1rem !important;
  }

  .me-md-4 {
    margin-right: 1.8rem !important;
  }

  .me-md-5 {
    margin-right: 3rem !important;
  }

  .me-md-6 {
    margin-right: 4rem !important;
  }

  .me-md-7 {
    margin-right: 5rem !important;
  }

  .me-md-8 {
    margin-right: 7.5rem !important;
  }

  .me-md-9 {
    margin-right: 10rem !important;
  }

  .me-md-10 {
    margin-right: 12.5rem !important;
  }

  .me-md-11 {
    margin-right: 15rem !important;
  }

  .me-md-auto {
    margin-right: auto !important;
  }

  .mb-md-0 {
    margin-bottom: 0 !important;
  }

  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-md-3 {
    margin-bottom: 1rem !important;
  }

  .mb-md-4 {
    margin-bottom: 1.8rem !important;
  }

  .mb-md-5 {
    margin-bottom: 3rem !important;
  }

  .mb-md-6 {
    margin-bottom: 4rem !important;
  }

  .mb-md-7 {
    margin-bottom: 5rem !important;
  }

  .mb-md-8 {
    margin-bottom: 7.5rem !important;
  }

  .mb-md-9 {
    margin-bottom: 10rem !important;
  }

  .mb-md-10 {
    margin-bottom: 12.5rem !important;
  }

  .mb-md-11 {
    margin-bottom: 15rem !important;
  }

  .mb-md-auto {
    margin-bottom: auto !important;
  }

  .ms-md-0 {
    margin-left: 0 !important;
  }

  .ms-md-1 {
    margin-left: 0.25rem !important;
  }

  .ms-md-2 {
    margin-left: 0.5rem !important;
  }

  .ms-md-3 {
    margin-left: 1rem !important;
  }

  .ms-md-4 {
    margin-left: 1.8rem !important;
  }

  .ms-md-5 {
    margin-left: 3rem !important;
  }

  .ms-md-6 {
    margin-left: 4rem !important;
  }

  .ms-md-7 {
    margin-left: 5rem !important;
  }

  .ms-md-8 {
    margin-left: 7.5rem !important;
  }

  .ms-md-9 {
    margin-left: 10rem !important;
  }

  .ms-md-10 {
    margin-left: 12.5rem !important;
  }

  .ms-md-11 {
    margin-left: 15rem !important;
  }

  .ms-md-auto {
    margin-left: auto !important;
  }

  .m-md-n1 {
    margin: -0.25rem !important;
  }

  .m-md-n2 {
    margin: -0.5rem !important;
  }

  .m-md-n3 {
    margin: -1rem !important;
  }

  .m-md-n4 {
    margin: -1.8rem !important;
  }

  .m-md-n5 {
    margin: -3rem !important;
  }

  .m-md-n6 {
    margin: -4rem !important;
  }

  .m-md-n7 {
    margin: -5rem !important;
  }

  .m-md-n8 {
    margin: -7.5rem !important;
  }

  .m-md-n9 {
    margin: -10rem !important;
  }

  .m-md-n10 {
    margin: -12.5rem !important;
  }

  .m-md-n11 {
    margin: -15rem !important;
  }

  .mx-md-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }

  .mx-md-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }

  .mx-md-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }

  .mx-md-n4 {
    margin-right: -1.8rem !important;
    margin-left: -1.8rem !important;
  }

  .mx-md-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }

  .mx-md-n6 {
    margin-right: -4rem !important;
    margin-left: -4rem !important;
  }

  .mx-md-n7 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-md-n8 {
    margin-right: -7.5rem !important;
    margin-left: -7.5rem !important;
  }

  .mx-md-n9 {
    margin-right: -10rem !important;
    margin-left: -10rem !important;
  }

  .mx-md-n10 {
    margin-right: -12.5rem !important;
    margin-left: -12.5rem !important;
  }

  .mx-md-n11 {
    margin-right: -15rem !important;
    margin-left: -15rem !important;
  }

  .my-md-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }

  .my-md-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }

  .my-md-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }

  .my-md-n4 {
    margin-top: -1.8rem !important;
    margin-bottom: -1.8rem !important;
  }

  .my-md-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }

  .my-md-n6 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }

  .my-md-n7 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-md-n8 {
    margin-top: -7.5rem !important;
    margin-bottom: -7.5rem !important;
  }

  .my-md-n9 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }

  .my-md-n10 {
    margin-top: -12.5rem !important;
    margin-bottom: -12.5rem !important;
  }

  .my-md-n11 {
    margin-top: -15rem !important;
    margin-bottom: -15rem !important;
  }

  .mt-md-n1 {
    margin-top: -0.25rem !important;
  }

  .mt-md-n2 {
    margin-top: -0.5rem !important;
  }

  .mt-md-n3 {
    margin-top: -1rem !important;
  }

  .mt-md-n4 {
    margin-top: -1.8rem !important;
  }

  .mt-md-n5 {
    margin-top: -3rem !important;
  }

  .mt-md-n6 {
    margin-top: -4rem !important;
  }

  .mt-md-n7 {
    margin-top: -5rem !important;
  }

  .mt-md-n8 {
    margin-top: -7.5rem !important;
  }

  .mt-md-n9 {
    margin-top: -10rem !important;
  }

  .mt-md-n10 {
    margin-top: -12.5rem !important;
  }

  .mt-md-n11 {
    margin-top: -15rem !important;
  }

  .me-md-n1 {
    margin-right: -0.25rem !important;
  }

  .me-md-n2 {
    margin-right: -0.5rem !important;
  }

  .me-md-n3 {
    margin-right: -1rem !important;
  }

  .me-md-n4 {
    margin-right: -1.8rem !important;
  }

  .me-md-n5 {
    margin-right: -3rem !important;
  }

  .me-md-n6 {
    margin-right: -4rem !important;
  }

  .me-md-n7 {
    margin-right: -5rem !important;
  }

  .me-md-n8 {
    margin-right: -7.5rem !important;
  }

  .me-md-n9 {
    margin-right: -10rem !important;
  }

  .me-md-n10 {
    margin-right: -12.5rem !important;
  }

  .me-md-n11 {
    margin-right: -15rem !important;
  }

  .mb-md-n1 {
    margin-bottom: -0.25rem !important;
  }

  .mb-md-n2 {
    margin-bottom: -0.5rem !important;
  }

  .mb-md-n3 {
    margin-bottom: -1rem !important;
  }

  .mb-md-n4 {
    margin-bottom: -1.8rem !important;
  }

  .mb-md-n5 {
    margin-bottom: -3rem !important;
  }

  .mb-md-n6 {
    margin-bottom: -4rem !important;
  }

  .mb-md-n7 {
    margin-bottom: -5rem !important;
  }

  .mb-md-n8 {
    margin-bottom: -7.5rem !important;
  }

  .mb-md-n9 {
    margin-bottom: -10rem !important;
  }

  .mb-md-n10 {
    margin-bottom: -12.5rem !important;
  }

  .mb-md-n11 {
    margin-bottom: -15rem !important;
  }

  .ms-md-n1 {
    margin-left: -0.25rem !important;
  }

  .ms-md-n2 {
    margin-left: -0.5rem !important;
  }

  .ms-md-n3 {
    margin-left: -1rem !important;
  }

  .ms-md-n4 {
    margin-left: -1.8rem !important;
  }

  .ms-md-n5 {
    margin-left: -3rem !important;
  }

  .ms-md-n6 {
    margin-left: -4rem !important;
  }

  .ms-md-n7 {
    margin-left: -5rem !important;
  }

  .ms-md-n8 {
    margin-left: -7.5rem !important;
  }

  .ms-md-n9 {
    margin-left: -10rem !important;
  }

  .ms-md-n10 {
    margin-left: -12.5rem !important;
  }

  .ms-md-n11 {
    margin-left: -15rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .p-md-1 {
    padding: 0.25rem !important;
  }

  .p-md-2 {
    padding: 0.5rem !important;
  }

  .p-md-3 {
    padding: 1rem !important;
  }

  .p-md-4 {
    padding: 1.8rem !important;
  }

  .p-md-5 {
    padding: 3rem !important;
  }

  .p-md-6 {
    padding: 4rem !important;
  }

  .p-md-7 {
    padding: 5rem !important;
  }

  .p-md-8 {
    padding: 7.5rem !important;
  }

  .p-md-9 {
    padding: 10rem !important;
  }

  .p-md-10 {
    padding: 12.5rem !important;
  }

  .p-md-11 {
    padding: 15rem !important;
  }

  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-md-4 {
    padding-right: 1.8rem !important;
    padding-left: 1.8rem !important;
  }

  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .px-md-6 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }

  .px-md-7 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-md-8 {
    padding-right: 7.5rem !important;
    padding-left: 7.5rem !important;
  }

  .px-md-9 {
    padding-right: 10rem !important;
    padding-left: 10rem !important;
  }

  .px-md-10 {
    padding-right: 12.5rem !important;
    padding-left: 12.5rem !important;
  }

  .px-md-11 {
    padding-right: 15rem !important;
    padding-left: 15rem !important;
  }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-md-4 {
    padding-top: 1.8rem !important;
    padding-bottom: 1.8rem !important;
  }

  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-md-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-md-7 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-md-8 {
    padding-top: 7.5rem !important;
    padding-bottom: 7.5rem !important;
  }

  .py-md-9 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }

  .py-md-10 {
    padding-top: 12.5rem !important;
    padding-bottom: 12.5rem !important;
  }

  .py-md-11 {
    padding-top: 15rem !important;
    padding-bottom: 15rem !important;
  }

  .pt-md-0 {
    padding-top: 0 !important;
  }

  .pt-md-1 {
    padding-top: 0.25rem !important;
  }

  .pt-md-2 {
    padding-top: 0.5rem !important;
  }

  .pt-md-3 {
    padding-top: 1rem !important;
  }

  .pt-md-4 {
    padding-top: 1.8rem !important;
  }

  .pt-md-5 {
    padding-top: 3rem !important;
  }

  .pt-md-6 {
    padding-top: 4rem !important;
  }

  .pt-md-7 {
    padding-top: 5rem !important;
  }

  .pt-md-8 {
    padding-top: 7.5rem !important;
  }

  .pt-md-9 {
    padding-top: 10rem !important;
  }

  .pt-md-10 {
    padding-top: 12.5rem !important;
  }

  .pt-md-11 {
    padding-top: 15rem !important;
  }

  .pe-md-0 {
    padding-right: 0 !important;
  }

  .pe-md-1 {
    padding-right: 0.25rem !important;
  }

  .pe-md-2 {
    padding-right: 0.5rem !important;
  }

  .pe-md-3 {
    padding-right: 1rem !important;
  }

  .pe-md-4 {
    padding-right: 1.8rem !important;
  }

  .pe-md-5 {
    padding-right: 3rem !important;
  }

  .pe-md-6 {
    padding-right: 4rem !important;
  }

  .pe-md-7 {
    padding-right: 5rem !important;
  }

  .pe-md-8 {
    padding-right: 7.5rem !important;
  }

  .pe-md-9 {
    padding-right: 10rem !important;
  }

  .pe-md-10 {
    padding-right: 12.5rem !important;
  }

  .pe-md-11 {
    padding-right: 15rem !important;
  }

  .pb-md-0 {
    padding-bottom: 0 !important;
  }

  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-md-3 {
    padding-bottom: 1rem !important;
  }

  .pb-md-4 {
    padding-bottom: 1.8rem !important;
  }

  .pb-md-5 {
    padding-bottom: 3rem !important;
  }

  .pb-md-6 {
    padding-bottom: 4rem !important;
  }

  .pb-md-7 {
    padding-bottom: 5rem !important;
  }

  .pb-md-8 {
    padding-bottom: 7.5rem !important;
  }

  .pb-md-9 {
    padding-bottom: 10rem !important;
  }

  .pb-md-10 {
    padding-bottom: 12.5rem !important;
  }

  .pb-md-11 {
    padding-bottom: 15rem !important;
  }

  .ps-md-0 {
    padding-left: 0 !important;
  }

  .ps-md-1 {
    padding-left: 0.25rem !important;
  }

  .ps-md-2 {
    padding-left: 0.5rem !important;
  }

  .ps-md-3 {
    padding-left: 1rem !important;
  }

  .ps-md-4 {
    padding-left: 1.8rem !important;
  }

  .ps-md-5 {
    padding-left: 3rem !important;
  }

  .ps-md-6 {
    padding-left: 4rem !important;
  }

  .ps-md-7 {
    padding-left: 5rem !important;
  }

  .ps-md-8 {
    padding-left: 7.5rem !important;
  }

  .ps-md-9 {
    padding-left: 10rem !important;
  }

  .ps-md-10 {
    padding-left: 12.5rem !important;
  }

  .ps-md-11 {
    padding-left: 15rem !important;
  }

  .fs-md--2 {
    font-size: 0.5627813555rem !important;
  }

  .fs-md--1 {
    font-size: 0.75rem !important;
  }

  .fs-md-0 {
    font-size: 1rem !important;
  }

  .fs-md-1 {
    font-size: 1.333rem !important;
  }

  .fs-md-2 {
    font-size: 1.777rem !important;
  }

  .fs-md-3 {
    font-size: 2.369rem !important;
  }

  .fs-md-4 {
    font-size: 3.157rem !important;
  }

  .fs-md-5 {
    font-size: 4.199rem !important;
  }

  .fs-md-6 {
    font-size: 5.584rem !important;
  }

  .fs-md-7 {
    font-size: 7.427rem !important;
  }

  .fs-md-8 {
    font-size: 9.878rem !important;
  }

  .text-md-start {
    text-align: left !important;
  }

  .text-md-end {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }

  .rounded-md-top {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
  }

  .rounded-md-top-lg {
    border-top-left-radius: 0.7rem !important;
    border-top-right-radius: 0.7rem !important;
  }

  .rounded-md-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .rounded-md-end {
    border-top-right-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
  }

  .rounded-md-end-lg {
    border-top-right-radius: 0.7rem !important;
    border-bottom-right-radius: 0.7rem !important;
  }

  .rounded-md-end-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .rounded-md-bottom {
    border-bottom-right-radius: 0.5rem !important;
    border-bottom-left-radius: 0.5rem !important;
  }

  .rounded-md-bottom-lg {
    border-bottom-right-radius: 0.7rem !important;
    border-bottom-left-radius: 0.7rem !important;
  }

  .rounded-md-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .rounded-md-start {
    border-bottom-left-radius: 0.5rem !important;
    border-top-left-radius: 0.5rem !important;
  }

  .rounded-md-start-lg {
    border-bottom-left-radius: 0.7rem !important;
    border-top-left-radius: 0.7rem !important;
  }

  .rounded-md-start-0 {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }

  .max-vh-md-25 {
    max-height: 25vh !important;
  }

  .max-vh-md-50 {
    max-height: 50vh !important;
  }

  .max-vh-md-75 {
    max-height: 75vh !important;
  }

  .max-vh-md-100 {
    max-height: 100vh !important;
  }

  .border-md-x {
    border-left: 1px solid var(--gohub-border-color) !important;
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
  }

  .border-md-y {
    border-top: 1px solid var(--gohub-border-color) !important;
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-md-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  .border-md-dashed {
    border: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-top {
    border-top: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-end {
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-start {
    border-left: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-bottom {
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-x {
    border-left: 1px dashed var(--gohub-border-color) !important;
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-md-dashed-y {
    border-top: 1px dashed var(--gohub-border-color) !important;
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .rounded-md-0 {
    border-radius: 0 !important;
  }
}
@media (min-width: 960px) {
  .float-lg-start {
    float: left !important;
  }

  .float-lg-end {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }

  .opacity-lg-0 {
    opacity: 0 !important;
  }

  .opacity-lg-25 {
    opacity: 0.25 !important;
  }

  .opacity-lg-50 {
    opacity: 0.5 !important;
  }

  .opacity-lg-75 {
    opacity: 0.75 !important;
  }

  .opacity-lg-85 {
    opacity: 0.85 !important;
  }

  .opacity-lg-100 {
    opacity: 1 !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-grid {
    display: grid !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-lg-none {
    display: none !important;
  }

  .position-lg-static {
    position: static !important;
  }

  .position-lg-absolute {
    position: absolute !important;
  }

  .position-lg-relative {
    position: relative !important;
  }

  .position-lg-fixed {
    position: fixed !important;
  }

  .position-lg-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }

  .translate-lg-middle {
    -webkit-transform: translateX(-50%) translateY(-50%) !important;
    -ms-transform: translateX(-50%) translateY(-50%) !important;
    transform: translateX(-50%) translateY(-50%) !important;
  }

  .translate-lg-middle-x {
    -webkit-transform: translateX(-50%) !important;
    -ms-transform: translateX(-50%) !important;
    transform: translateX(-50%) !important;
  }

  .translate-lg-middle-y {
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
  }

  .border-lg {
    border: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-0 {
    border: 0 !important;
  }

  .border-lg-top {
    border-top: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-top-0 {
    border-top: 0 !important;
  }

  .border-lg-end {
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-end-0 {
    border-right: 0 !important;
  }

  .border-lg-bottom {
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-bottom-0 {
    border-bottom: 0 !important;
  }

  .border-lg-start {
    border-left: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-start-0 {
    border-left: 0 !important;
  }

  .border-lg-facebook {
    border-color: #3c5a99 !important;
  }

  .border-lg-google-plus {
    border-color: #dd4b39 !important;
  }

  .border-lg-twitter {
    border-color: #1da1f2 !important;
  }

  .border-lg-linkedin {
    border-color: #0077b5 !important;
  }

  .border-lg-youtube {
    border-color: #ff0001 !important;
  }

  .border-lg-github {
    border-color: #333333 !important;
  }

  .border-lg-black {
    border-color: #000 !important;
  }

  .border-lg-100 {
    border-color: #F5F2FC !important;
  }

  .border-lg-200 {
    border-color: #f2f2f2 !important;
  }

  .border-lg-300 {
    border-color: #E7E4EE !important;
  }

  .border-lg-400 {
    border-color: #bebebe !important;
  }

  .border-lg-500 {
    border-color: #949494 !important;
  }

  .border-lg-600 {
    border-color: #7F7F7F !important;
  }

  .border-lg-700 {
    border-color: #717075 !important;
  }

  .border-lg-800 {
    border-color: #5E5D61 !important;
  }

  .border-lg-900 {
    border-color: #403F42 !important;
  }

  .border-lg-1000 {
    border-color: #212240 !important;
  }

  .border-lg-1100 {
    border-color: #1c1c1c !important;
  }

  .border-lg-white {
    border-color: #fff !important;
  }

  .border-lg-primary {
    border-color: #6f42c1 !important;
  }

  .border-lg-secondary {
    border-color: #D032D0 !important;
  }

  .border-lg-success {
    border-color: #7ed321 !important;
  }

  .border-lg-info {
    border-color: #1C16AF !important;
  }

  .border-lg-warning {
    border-color: #f37f29 !important;
  }

  .border-lg-danger {
    border-color: #d0021b !important;
  }

  .border-lg-light {
    border-color: #F5F2FC !important;
  }

  .border-lg-dark {
    border-color: #1c1c1c !important;
  }

  .w-lg-25 {
    width: 25% !important;
  }

  .w-lg-50 {
    width: 50% !important;
  }

  .w-lg-75 {
    width: 75% !important;
  }

  .w-lg-100 {
    width: 100% !important;
  }

  .w-lg-auto {
    width: auto !important;
  }

  .vw-lg-25 {
    width: 25vw !important;
  }

  .vw-lg-50 {
    width: 50vw !important;
  }

  .vw-lg-75 {
    width: 75vw !important;
  }

  .vw-lg-100 {
    width: 100vw !important;
  }

  .h-lg-25 {
    height: 25% !important;
  }

  .h-lg-50 {
    height: 50% !important;
  }

  .h-lg-75 {
    height: 75% !important;
  }

  .h-lg-100 {
    height: 100% !important;
  }

  .h-lg-auto {
    height: auto !important;
  }

  .vh-lg-25 {
    height: 25vh !important;
  }

  .vh-lg-50 {
    height: 50vh !important;
  }

  .vh-lg-75 {
    height: 75vh !important;
  }

  .vh-lg-100 {
    height: 100vh !important;
  }

  .min-vh-lg-25 {
    min-height: 25vh !important;
  }

  .min-vh-lg-50 {
    min-height: 50vh !important;
  }

  .min-vh-lg-75 {
    min-height: 75vh !important;
  }

  .min-vh-lg-100 {
    min-height: 100vh !important;
  }

  .flex-lg-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-lg-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-lg-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-lg-grow-0 {
    -webkit-box-flex: 0 !important;
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .gap-lg-0 {
    gap: 0 !important;
  }

  .gap-lg-1 {
    gap: 0.25rem !important;
  }

  .gap-lg-2 {
    gap: 0.5rem !important;
  }

  .gap-lg-3 {
    gap: 1rem !important;
  }

  .gap-lg-4 {
    gap: 1.8rem !important;
  }

  .gap-lg-5 {
    gap: 3rem !important;
  }

  .gap-lg-6 {
    gap: 4rem !important;
  }

  .gap-lg-7 {
    gap: 5rem !important;
  }

  .gap-lg-8 {
    gap: 7.5rem !important;
  }

  .gap-lg-9 {
    gap: 10rem !important;
  }

  .gap-lg-10 {
    gap: 12.5rem !important;
  }

  .gap-lg-11 {
    gap: 15rem !important;
  }

  .justify-content-lg-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-lg-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .justify-content-lg-evenly {
    -webkit-box-pack: space-evenly !important;
    -ms-flex-pack: space-evenly !important;
    justify-content: space-evenly !important;
  }

  .align-items-lg-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-lg-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }

  .order-lg-first {
    -webkit-box-ordinal-group: 0 !important;
    -ms-flex-order: -1 !important;
    order: -1 !important;
  }

  .order-lg-0 {
    -webkit-box-ordinal-group: 1 !important;
    -ms-flex-order: 0 !important;
    order: 0 !important;
  }

  .order-lg-1 {
    -webkit-box-ordinal-group: 2 !important;
    -ms-flex-order: 1 !important;
    order: 1 !important;
  }

  .order-lg-2 {
    -webkit-box-ordinal-group: 3 !important;
    -ms-flex-order: 2 !important;
    order: 2 !important;
  }

  .order-lg-3 {
    -webkit-box-ordinal-group: 4 !important;
    -ms-flex-order: 3 !important;
    order: 3 !important;
  }

  .order-lg-4 {
    -webkit-box-ordinal-group: 5 !important;
    -ms-flex-order: 4 !important;
    order: 4 !important;
  }

  .order-lg-5 {
    -webkit-box-ordinal-group: 6 !important;
    -ms-flex-order: 5 !important;
    order: 5 !important;
  }

  .order-lg-last {
    -webkit-box-ordinal-group: 7 !important;
    -ms-flex-order: 6 !important;
    order: 6 !important;
  }

  .m-lg-0 {
    margin: 0 !important;
  }

  .m-lg-1 {
    margin: 0.25rem !important;
  }

  .m-lg-2 {
    margin: 0.5rem !important;
  }

  .m-lg-3 {
    margin: 1rem !important;
  }

  .m-lg-4 {
    margin: 1.8rem !important;
  }

  .m-lg-5 {
    margin: 3rem !important;
  }

  .m-lg-6 {
    margin: 4rem !important;
  }

  .m-lg-7 {
    margin: 5rem !important;
  }

  .m-lg-8 {
    margin: 7.5rem !important;
  }

  .m-lg-9 {
    margin: 10rem !important;
  }

  .m-lg-10 {
    margin: 12.5rem !important;
  }

  .m-lg-11 {
    margin: 15rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-lg-4 {
    margin-right: 1.8rem !important;
    margin-left: 1.8rem !important;
  }

  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-lg-6 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }

  .mx-lg-7 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-lg-8 {
    margin-right: 7.5rem !important;
    margin-left: 7.5rem !important;
  }

  .mx-lg-9 {
    margin-right: 10rem !important;
    margin-left: 10rem !important;
  }

  .mx-lg-10 {
    margin-right: 12.5rem !important;
    margin-left: 12.5rem !important;
  }

  .mx-lg-11 {
    margin-right: 15rem !important;
    margin-left: 15rem !important;
  }

  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-lg-4 {
    margin-top: 1.8rem !important;
    margin-bottom: 1.8rem !important;
  }

  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-lg-6 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }

  .my-lg-7 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-lg-8 {
    margin-top: 7.5rem !important;
    margin-bottom: 7.5rem !important;
  }

  .my-lg-9 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }

  .my-lg-10 {
    margin-top: 12.5rem !important;
    margin-bottom: 12.5rem !important;
  }

  .my-lg-11 {
    margin-top: 15rem !important;
    margin-bottom: 15rem !important;
  }

  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-lg-0 {
    margin-top: 0 !important;
  }

  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }

  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }

  .mt-lg-3 {
    margin-top: 1rem !important;
  }

  .mt-lg-4 {
    margin-top: 1.8rem !important;
  }

  .mt-lg-5 {
    margin-top: 3rem !important;
  }

  .mt-lg-6 {
    margin-top: 4rem !important;
  }

  .mt-lg-7 {
    margin-top: 5rem !important;
  }

  .mt-lg-8 {
    margin-top: 7.5rem !important;
  }

  .mt-lg-9 {
    margin-top: 10rem !important;
  }

  .mt-lg-10 {
    margin-top: 12.5rem !important;
  }

  .mt-lg-11 {
    margin-top: 15rem !important;
  }

  .mt-lg-auto {
    margin-top: auto !important;
  }

  .me-lg-0 {
    margin-right: 0 !important;
  }

  .me-lg-1 {
    margin-right: 0.25rem !important;
  }

  .me-lg-2 {
    margin-right: 0.5rem !important;
  }

  .me-lg-3 {
    margin-right: 1rem !important;
  }

  .me-lg-4 {
    margin-right: 1.8rem !important;
  }

  .me-lg-5 {
    margin-right: 3rem !important;
  }

  .me-lg-6 {
    margin-right: 4rem !important;
  }

  .me-lg-7 {
    margin-right: 5rem !important;
  }

  .me-lg-8 {
    margin-right: 7.5rem !important;
  }

  .me-lg-9 {
    margin-right: 10rem !important;
  }

  .me-lg-10 {
    margin-right: 12.5rem !important;
  }

  .me-lg-11 {
    margin-right: 15rem !important;
  }

  .me-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-0 {
    margin-bottom: 0 !important;
  }

  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }

  .mb-lg-4 {
    margin-bottom: 1.8rem !important;
  }

  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }

  .mb-lg-6 {
    margin-bottom: 4rem !important;
  }

  .mb-lg-7 {
    margin-bottom: 5rem !important;
  }

  .mb-lg-8 {
    margin-bottom: 7.5rem !important;
  }

  .mb-lg-9 {
    margin-bottom: 10rem !important;
  }

  .mb-lg-10 {
    margin-bottom: 12.5rem !important;
  }

  .mb-lg-11 {
    margin-bottom: 15rem !important;
  }

  .mb-lg-auto {
    margin-bottom: auto !important;
  }

  .ms-lg-0 {
    margin-left: 0 !important;
  }

  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }

  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }

  .ms-lg-3 {
    margin-left: 1rem !important;
  }

  .ms-lg-4 {
    margin-left: 1.8rem !important;
  }

  .ms-lg-5 {
    margin-left: 3rem !important;
  }

  .ms-lg-6 {
    margin-left: 4rem !important;
  }

  .ms-lg-7 {
    margin-left: 5rem !important;
  }

  .ms-lg-8 {
    margin-left: 7.5rem !important;
  }

  .ms-lg-9 {
    margin-left: 10rem !important;
  }

  .ms-lg-10 {
    margin-left: 12.5rem !important;
  }

  .ms-lg-11 {
    margin-left: 15rem !important;
  }

  .ms-lg-auto {
    margin-left: auto !important;
  }

  .m-lg-n1 {
    margin: -0.25rem !important;
  }

  .m-lg-n2 {
    margin: -0.5rem !important;
  }

  .m-lg-n3 {
    margin: -1rem !important;
  }

  .m-lg-n4 {
    margin: -1.8rem !important;
  }

  .m-lg-n5 {
    margin: -3rem !important;
  }

  .m-lg-n6 {
    margin: -4rem !important;
  }

  .m-lg-n7 {
    margin: -5rem !important;
  }

  .m-lg-n8 {
    margin: -7.5rem !important;
  }

  .m-lg-n9 {
    margin: -10rem !important;
  }

  .m-lg-n10 {
    margin: -12.5rem !important;
  }

  .m-lg-n11 {
    margin: -15rem !important;
  }

  .mx-lg-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }

  .mx-lg-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }

  .mx-lg-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }

  .mx-lg-n4 {
    margin-right: -1.8rem !important;
    margin-left: -1.8rem !important;
  }

  .mx-lg-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }

  .mx-lg-n6 {
    margin-right: -4rem !important;
    margin-left: -4rem !important;
  }

  .mx-lg-n7 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-lg-n8 {
    margin-right: -7.5rem !important;
    margin-left: -7.5rem !important;
  }

  .mx-lg-n9 {
    margin-right: -10rem !important;
    margin-left: -10rem !important;
  }

  .mx-lg-n10 {
    margin-right: -12.5rem !important;
    margin-left: -12.5rem !important;
  }

  .mx-lg-n11 {
    margin-right: -15rem !important;
    margin-left: -15rem !important;
  }

  .my-lg-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }

  .my-lg-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }

  .my-lg-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }

  .my-lg-n4 {
    margin-top: -1.8rem !important;
    margin-bottom: -1.8rem !important;
  }

  .my-lg-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }

  .my-lg-n6 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }

  .my-lg-n7 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-lg-n8 {
    margin-top: -7.5rem !important;
    margin-bottom: -7.5rem !important;
  }

  .my-lg-n9 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }

  .my-lg-n10 {
    margin-top: -12.5rem !important;
    margin-bottom: -12.5rem !important;
  }

  .my-lg-n11 {
    margin-top: -15rem !important;
    margin-bottom: -15rem !important;
  }

  .mt-lg-n1 {
    margin-top: -0.25rem !important;
  }

  .mt-lg-n2 {
    margin-top: -0.5rem !important;
  }

  .mt-lg-n3 {
    margin-top: -1rem !important;
  }

  .mt-lg-n4 {
    margin-top: -1.8rem !important;
  }

  .mt-lg-n5 {
    margin-top: -3rem !important;
  }

  .mt-lg-n6 {
    margin-top: -4rem !important;
  }

  .mt-lg-n7 {
    margin-top: -5rem !important;
  }

  .mt-lg-n8 {
    margin-top: -7.5rem !important;
  }

  .mt-lg-n9 {
    margin-top: -10rem !important;
  }

  .mt-lg-n10 {
    margin-top: -12.5rem !important;
  }

  .mt-lg-n11 {
    margin-top: -15rem !important;
  }

  .me-lg-n1 {
    margin-right: -0.25rem !important;
  }

  .me-lg-n2 {
    margin-right: -0.5rem !important;
  }

  .me-lg-n3 {
    margin-right: -1rem !important;
  }

  .me-lg-n4 {
    margin-right: -1.8rem !important;
  }

  .me-lg-n5 {
    margin-right: -3rem !important;
  }

  .me-lg-n6 {
    margin-right: -4rem !important;
  }

  .me-lg-n7 {
    margin-right: -5rem !important;
  }

  .me-lg-n8 {
    margin-right: -7.5rem !important;
  }

  .me-lg-n9 {
    margin-right: -10rem !important;
  }

  .me-lg-n10 {
    margin-right: -12.5rem !important;
  }

  .me-lg-n11 {
    margin-right: -15rem !important;
  }

  .mb-lg-n1 {
    margin-bottom: -0.25rem !important;
  }

  .mb-lg-n2 {
    margin-bottom: -0.5rem !important;
  }

  .mb-lg-n3 {
    margin-bottom: -1rem !important;
  }

  .mb-lg-n4 {
    margin-bottom: -1.8rem !important;
  }

  .mb-lg-n5 {
    margin-bottom: -3rem !important;
  }

  .mb-lg-n6 {
    margin-bottom: -4rem !important;
  }

  .mb-lg-n7 {
    margin-bottom: -5rem !important;
  }

  .mb-lg-n8 {
    margin-bottom: -7.5rem !important;
  }

  .mb-lg-n9 {
    margin-bottom: -10rem !important;
  }

  .mb-lg-n10 {
    margin-bottom: -12.5rem !important;
  }

  .mb-lg-n11 {
    margin-bottom: -15rem !important;
  }

  .ms-lg-n1 {
    margin-left: -0.25rem !important;
  }

  .ms-lg-n2 {
    margin-left: -0.5rem !important;
  }

  .ms-lg-n3 {
    margin-left: -1rem !important;
  }

  .ms-lg-n4 {
    margin-left: -1.8rem !important;
  }

  .ms-lg-n5 {
    margin-left: -3rem !important;
  }

  .ms-lg-n6 {
    margin-left: -4rem !important;
  }

  .ms-lg-n7 {
    margin-left: -5rem !important;
  }

  .ms-lg-n8 {
    margin-left: -7.5rem !important;
  }

  .ms-lg-n9 {
    margin-left: -10rem !important;
  }

  .ms-lg-n10 {
    margin-left: -12.5rem !important;
  }

  .ms-lg-n11 {
    margin-left: -15rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .p-lg-1 {
    padding: 0.25rem !important;
  }

  .p-lg-2 {
    padding: 0.5rem !important;
  }

  .p-lg-3 {
    padding: 1rem !important;
  }

  .p-lg-4 {
    padding: 1.8rem !important;
  }

  .p-lg-5 {
    padding: 3rem !important;
  }

  .p-lg-6 {
    padding: 4rem !important;
  }

  .p-lg-7 {
    padding: 5rem !important;
  }

  .p-lg-8 {
    padding: 7.5rem !important;
  }

  .p-lg-9 {
    padding: 10rem !important;
  }

  .p-lg-10 {
    padding: 12.5rem !important;
  }

  .p-lg-11 {
    padding: 15rem !important;
  }

  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-lg-4 {
    padding-right: 1.8rem !important;
    padding-left: 1.8rem !important;
  }

  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .px-lg-6 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }

  .px-lg-7 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-lg-8 {
    padding-right: 7.5rem !important;
    padding-left: 7.5rem !important;
  }

  .px-lg-9 {
    padding-right: 10rem !important;
    padding-left: 10rem !important;
  }

  .px-lg-10 {
    padding-right: 12.5rem !important;
    padding-left: 12.5rem !important;
  }

  .px-lg-11 {
    padding-right: 15rem !important;
    padding-left: 15rem !important;
  }

  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-lg-4 {
    padding-top: 1.8rem !important;
    padding-bottom: 1.8rem !important;
  }

  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-lg-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-lg-7 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-lg-8 {
    padding-top: 7.5rem !important;
    padding-bottom: 7.5rem !important;
  }

  .py-lg-9 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }

  .py-lg-10 {
    padding-top: 12.5rem !important;
    padding-bottom: 12.5rem !important;
  }

  .py-lg-11 {
    padding-top: 15rem !important;
    padding-bottom: 15rem !important;
  }

  .pt-lg-0 {
    padding-top: 0 !important;
  }

  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }

  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }

  .pt-lg-3 {
    padding-top: 1rem !important;
  }

  .pt-lg-4 {
    padding-top: 1.8rem !important;
  }

  .pt-lg-5 {
    padding-top: 3rem !important;
  }

  .pt-lg-6 {
    padding-top: 4rem !important;
  }

  .pt-lg-7 {
    padding-top: 5rem !important;
  }

  .pt-lg-8 {
    padding-top: 7.5rem !important;
  }

  .pt-lg-9 {
    padding-top: 10rem !important;
  }

  .pt-lg-10 {
    padding-top: 12.5rem !important;
  }

  .pt-lg-11 {
    padding-top: 15rem !important;
  }

  .pe-lg-0 {
    padding-right: 0 !important;
  }

  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }

  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }

  .pe-lg-3 {
    padding-right: 1rem !important;
  }

  .pe-lg-4 {
    padding-right: 1.8rem !important;
  }

  .pe-lg-5 {
    padding-right: 3rem !important;
  }

  .pe-lg-6 {
    padding-right: 4rem !important;
  }

  .pe-lg-7 {
    padding-right: 5rem !important;
  }

  .pe-lg-8 {
    padding-right: 7.5rem !important;
  }

  .pe-lg-9 {
    padding-right: 10rem !important;
  }

  .pe-lg-10 {
    padding-right: 12.5rem !important;
  }

  .pe-lg-11 {
    padding-right: 15rem !important;
  }

  .pb-lg-0 {
    padding-bottom: 0 !important;
  }

  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }

  .pb-lg-4 {
    padding-bottom: 1.8rem !important;
  }

  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }

  .pb-lg-6 {
    padding-bottom: 4rem !important;
  }

  .pb-lg-7 {
    padding-bottom: 5rem !important;
  }

  .pb-lg-8 {
    padding-bottom: 7.5rem !important;
  }

  .pb-lg-9 {
    padding-bottom: 10rem !important;
  }

  .pb-lg-10 {
    padding-bottom: 12.5rem !important;
  }

  .pb-lg-11 {
    padding-bottom: 15rem !important;
  }

  .ps-lg-0 {
    padding-left: 0 !important;
  }

  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }

  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }

  .ps-lg-3 {
    padding-left: 1rem !important;
  }

  .ps-lg-4 {
    padding-left: 1.8rem !important;
  }

  .ps-lg-5 {
    padding-left: 3rem !important;
  }

  .ps-lg-6 {
    padding-left: 4rem !important;
  }

  .ps-lg-7 {
    padding-left: 5rem !important;
  }

  .ps-lg-8 {
    padding-left: 7.5rem !important;
  }

  .ps-lg-9 {
    padding-left: 10rem !important;
  }

  .ps-lg-10 {
    padding-left: 12.5rem !important;
  }

  .ps-lg-11 {
    padding-left: 15rem !important;
  }

  .fs-lg--2 {
    font-size: 0.5627813555rem !important;
  }

  .fs-lg--1 {
    font-size: 0.75rem !important;
  }

  .fs-lg-0 {
    font-size: 1rem !important;
  }

  .fs-lg-1 {
    font-size: 1.333rem !important;
  }

  .fs-lg-2 {
    font-size: 1.777rem !important;
  }

  .fs-lg-3 {
    font-size: 2.369rem !important;
  }

  .fs-lg-4 {
    font-size: 3.157rem !important;
  }

  .fs-lg-5 {
    font-size: 4.199rem !important;
  }

  .fs-lg-6 {
    font-size: 5.584rem !important;
  }

  .fs-lg-7 {
    font-size: 7.427rem !important;
  }

  .fs-lg-8 {
    font-size: 9.878rem !important;
  }

  .text-lg-start {
    text-align: left !important;
  }

  .text-lg-end {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }

  .rounded-lg-top {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
  }

  .rounded-lg-top-lg {
    border-top-left-radius: 0.7rem !important;
    border-top-right-radius: 0.7rem !important;
  }

  .rounded-lg-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .rounded-lg-end {
    border-top-right-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
  }

  .rounded-lg-end-lg {
    border-top-right-radius: 0.7rem !important;
    border-bottom-right-radius: 0.7rem !important;
  }

  .rounded-lg-end-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .rounded-lg-bottom {
    border-bottom-right-radius: 0.5rem !important;
    border-bottom-left-radius: 0.5rem !important;
  }

  .rounded-lg-bottom-lg {
    border-bottom-right-radius: 0.7rem !important;
    border-bottom-left-radius: 0.7rem !important;
  }

  .rounded-lg-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .rounded-lg-start {
    border-bottom-left-radius: 0.5rem !important;
    border-top-left-radius: 0.5rem !important;
  }

  .rounded-lg-start-lg {
    border-bottom-left-radius: 0.7rem !important;
    border-top-left-radius: 0.7rem !important;
  }

  .rounded-lg-start-0 {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }

  .max-vh-lg-25 {
    max-height: 25vh !important;
  }

  .max-vh-lg-50 {
    max-height: 50vh !important;
  }

  .max-vh-lg-75 {
    max-height: 75vh !important;
  }

  .max-vh-lg-100 {
    max-height: 100vh !important;
  }

  .border-lg-x {
    border-left: 1px solid var(--gohub-border-color) !important;
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
  }

  .border-lg-y {
    border-top: 1px solid var(--gohub-border-color) !important;
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-lg-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  .border-lg-dashed {
    border: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-top {
    border-top: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-end {
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-start {
    border-left: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-bottom {
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-x {
    border-left: 1px dashed var(--gohub-border-color) !important;
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-lg-dashed-y {
    border-top: 1px dashed var(--gohub-border-color) !important;
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .rounded-lg-0 {
    border-radius: 0 !important;
  }
}
@media (min-width: 1140px) {
  .float-xl-start {
    float: left !important;
  }

  .float-xl-end {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }

  .opacity-xl-0 {
    opacity: 0 !important;
  }

  .opacity-xl-25 {
    opacity: 0.25 !important;
  }

  .opacity-xl-50 {
    opacity: 0.5 !important;
  }

  .opacity-xl-75 {
    opacity: 0.75 !important;
  }

  .opacity-xl-85 {
    opacity: 0.85 !important;
  }

  .opacity-xl-100 {
    opacity: 1 !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-grid {
    display: grid !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-xl-none {
    display: none !important;
  }

  .position-xl-static {
    position: static !important;
  }

  .position-xl-absolute {
    position: absolute !important;
  }

  .position-xl-relative {
    position: relative !important;
  }

  .position-xl-fixed {
    position: fixed !important;
  }

  .position-xl-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }

  .translate-xl-middle {
    -webkit-transform: translateX(-50%) translateY(-50%) !important;
    -ms-transform: translateX(-50%) translateY(-50%) !important;
    transform: translateX(-50%) translateY(-50%) !important;
  }

  .translate-xl-middle-x {
    -webkit-transform: translateX(-50%) !important;
    -ms-transform: translateX(-50%) !important;
    transform: translateX(-50%) !important;
  }

  .translate-xl-middle-y {
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
  }

  .border-xl {
    border: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-0 {
    border: 0 !important;
  }

  .border-xl-top {
    border-top: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-top-0 {
    border-top: 0 !important;
  }

  .border-xl-end {
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-end-0 {
    border-right: 0 !important;
  }

  .border-xl-bottom {
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-bottom-0 {
    border-bottom: 0 !important;
  }

  .border-xl-start {
    border-left: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-start-0 {
    border-left: 0 !important;
  }

  .border-xl-facebook {
    border-color: #3c5a99 !important;
  }

  .border-xl-google-plus {
    border-color: #dd4b39 !important;
  }

  .border-xl-twitter {
    border-color: #1da1f2 !important;
  }

  .border-xl-linkedin {
    border-color: #0077b5 !important;
  }

  .border-xl-youtube {
    border-color: #ff0001 !important;
  }

  .border-xl-github {
    border-color: #333333 !important;
  }

  .border-xl-black {
    border-color: #000 !important;
  }

  .border-xl-100 {
    border-color: #F5F2FC !important;
  }

  .border-xl-200 {
    border-color: #f2f2f2 !important;
  }

  .border-xl-300 {
    border-color: #E7E4EE !important;
  }

  .border-xl-400 {
    border-color: #bebebe !important;
  }

  .border-xl-500 {
    border-color: #949494 !important;
  }

  .border-xl-600 {
    border-color: #7F7F7F !important;
  }

  .border-xl-700 {
    border-color: #717075 !important;
  }

  .border-xl-800 {
    border-color: #5E5D61 !important;
  }

  .border-xl-900 {
    border-color: #403F42 !important;
  }

  .border-xl-1000 {
    border-color: #212240 !important;
  }

  .border-xl-1100 {
    border-color: #1c1c1c !important;
  }

  .border-xl-white {
    border-color: #fff !important;
  }

  .border-xl-primary {
    border-color: #6f42c1 !important;
  }

  .border-xl-secondary {
    border-color: #D032D0 !important;
  }

  .border-xl-success {
    border-color: #7ed321 !important;
  }

  .border-xl-info {
    border-color: #1C16AF !important;
  }

  .border-xl-warning {
    border-color: #f37f29 !important;
  }

  .border-xl-danger {
    border-color: #d0021b !important;
  }

  .border-xl-light {
    border-color: #F5F2FC !important;
  }

  .border-xl-dark {
    border-color: #1c1c1c !important;
  }

  .w-xl-25 {
    width: 25% !important;
  }

  .w-xl-50 {
    width: 50% !important;
  }

  .w-xl-75 {
    width: 75% !important;
  }

  .w-xl-100 {
    width: 100% !important;
  }

  .w-xl-auto {
    width: auto !important;
  }

  .vw-xl-25 {
    width: 25vw !important;
  }

  .vw-xl-50 {
    width: 50vw !important;
  }

  .vw-xl-75 {
    width: 75vw !important;
  }

  .vw-xl-100 {
    width: 100vw !important;
  }

  .h-xl-25 {
    height: 25% !important;
  }

  .h-xl-50 {
    height: 50% !important;
  }

  .h-xl-75 {
    height: 75% !important;
  }

  .h-xl-100 {
    height: 100% !important;
  }

  .h-xl-auto {
    height: auto !important;
  }

  .vh-xl-25 {
    height: 25vh !important;
  }

  .vh-xl-50 {
    height: 50vh !important;
  }

  .vh-xl-75 {
    height: 75vh !important;
  }

  .vh-xl-100 {
    height: 100vh !important;
  }

  .min-vh-xl-25 {
    min-height: 25vh !important;
  }

  .min-vh-xl-50 {
    min-height: 50vh !important;
  }

  .min-vh-xl-75 {
    min-height: 75vh !important;
  }

  .min-vh-xl-100 {
    min-height: 100vh !important;
  }

  .flex-xl-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-xl-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-xl-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-xl-grow-0 {
    -webkit-box-flex: 0 !important;
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .gap-xl-0 {
    gap: 0 !important;
  }

  .gap-xl-1 {
    gap: 0.25rem !important;
  }

  .gap-xl-2 {
    gap: 0.5rem !important;
  }

  .gap-xl-3 {
    gap: 1rem !important;
  }

  .gap-xl-4 {
    gap: 1.8rem !important;
  }

  .gap-xl-5 {
    gap: 3rem !important;
  }

  .gap-xl-6 {
    gap: 4rem !important;
  }

  .gap-xl-7 {
    gap: 5rem !important;
  }

  .gap-xl-8 {
    gap: 7.5rem !important;
  }

  .gap-xl-9 {
    gap: 10rem !important;
  }

  .gap-xl-10 {
    gap: 12.5rem !important;
  }

  .gap-xl-11 {
    gap: 15rem !important;
  }

  .justify-content-xl-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-xl-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .justify-content-xl-evenly {
    -webkit-box-pack: space-evenly !important;
    -ms-flex-pack: space-evenly !important;
    justify-content: space-evenly !important;
  }

  .align-items-xl-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-xl-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }

  .order-xl-first {
    -webkit-box-ordinal-group: 0 !important;
    -ms-flex-order: -1 !important;
    order: -1 !important;
  }

  .order-xl-0 {
    -webkit-box-ordinal-group: 1 !important;
    -ms-flex-order: 0 !important;
    order: 0 !important;
  }

  .order-xl-1 {
    -webkit-box-ordinal-group: 2 !important;
    -ms-flex-order: 1 !important;
    order: 1 !important;
  }

  .order-xl-2 {
    -webkit-box-ordinal-group: 3 !important;
    -ms-flex-order: 2 !important;
    order: 2 !important;
  }

  .order-xl-3 {
    -webkit-box-ordinal-group: 4 !important;
    -ms-flex-order: 3 !important;
    order: 3 !important;
  }

  .order-xl-4 {
    -webkit-box-ordinal-group: 5 !important;
    -ms-flex-order: 4 !important;
    order: 4 !important;
  }

  .order-xl-5 {
    -webkit-box-ordinal-group: 6 !important;
    -ms-flex-order: 5 !important;
    order: 5 !important;
  }

  .order-xl-last {
    -webkit-box-ordinal-group: 7 !important;
    -ms-flex-order: 6 !important;
    order: 6 !important;
  }

  .m-xl-0 {
    margin: 0 !important;
  }

  .m-xl-1 {
    margin: 0.25rem !important;
  }

  .m-xl-2 {
    margin: 0.5rem !important;
  }

  .m-xl-3 {
    margin: 1rem !important;
  }

  .m-xl-4 {
    margin: 1.8rem !important;
  }

  .m-xl-5 {
    margin: 3rem !important;
  }

  .m-xl-6 {
    margin: 4rem !important;
  }

  .m-xl-7 {
    margin: 5rem !important;
  }

  .m-xl-8 {
    margin: 7.5rem !important;
  }

  .m-xl-9 {
    margin: 10rem !important;
  }

  .m-xl-10 {
    margin: 12.5rem !important;
  }

  .m-xl-11 {
    margin: 15rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-xl-4 {
    margin-right: 1.8rem !important;
    margin-left: 1.8rem !important;
  }

  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-xl-6 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }

  .mx-xl-7 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-xl-8 {
    margin-right: 7.5rem !important;
    margin-left: 7.5rem !important;
  }

  .mx-xl-9 {
    margin-right: 10rem !important;
    margin-left: 10rem !important;
  }

  .mx-xl-10 {
    margin-right: 12.5rem !important;
    margin-left: 12.5rem !important;
  }

  .mx-xl-11 {
    margin-right: 15rem !important;
    margin-left: 15rem !important;
  }

  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-xl-4 {
    margin-top: 1.8rem !important;
    margin-bottom: 1.8rem !important;
  }

  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-xl-6 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }

  .my-xl-7 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-xl-8 {
    margin-top: 7.5rem !important;
    margin-bottom: 7.5rem !important;
  }

  .my-xl-9 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }

  .my-xl-10 {
    margin-top: 12.5rem !important;
    margin-bottom: 12.5rem !important;
  }

  .my-xl-11 {
    margin-top: 15rem !important;
    margin-bottom: 15rem !important;
  }

  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xl-0 {
    margin-top: 0 !important;
  }

  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }

  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }

  .mt-xl-3 {
    margin-top: 1rem !important;
  }

  .mt-xl-4 {
    margin-top: 1.8rem !important;
  }

  .mt-xl-5 {
    margin-top: 3rem !important;
  }

  .mt-xl-6 {
    margin-top: 4rem !important;
  }

  .mt-xl-7 {
    margin-top: 5rem !important;
  }

  .mt-xl-8 {
    margin-top: 7.5rem !important;
  }

  .mt-xl-9 {
    margin-top: 10rem !important;
  }

  .mt-xl-10 {
    margin-top: 12.5rem !important;
  }

  .mt-xl-11 {
    margin-top: 15rem !important;
  }

  .mt-xl-auto {
    margin-top: auto !important;
  }

  .me-xl-0 {
    margin-right: 0 !important;
  }

  .me-xl-1 {
    margin-right: 0.25rem !important;
  }

  .me-xl-2 {
    margin-right: 0.5rem !important;
  }

  .me-xl-3 {
    margin-right: 1rem !important;
  }

  .me-xl-4 {
    margin-right: 1.8rem !important;
  }

  .me-xl-5 {
    margin-right: 3rem !important;
  }

  .me-xl-6 {
    margin-right: 4rem !important;
  }

  .me-xl-7 {
    margin-right: 5rem !important;
  }

  .me-xl-8 {
    margin-right: 7.5rem !important;
  }

  .me-xl-9 {
    margin-right: 10rem !important;
  }

  .me-xl-10 {
    margin-right: 12.5rem !important;
  }

  .me-xl-11 {
    margin-right: 15rem !important;
  }

  .me-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }

  .mb-xl-4 {
    margin-bottom: 1.8rem !important;
  }

  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }

  .mb-xl-6 {
    margin-bottom: 4rem !important;
  }

  .mb-xl-7 {
    margin-bottom: 5rem !important;
  }

  .mb-xl-8 {
    margin-bottom: 7.5rem !important;
  }

  .mb-xl-9 {
    margin-bottom: 10rem !important;
  }

  .mb-xl-10 {
    margin-bottom: 12.5rem !important;
  }

  .mb-xl-11 {
    margin-bottom: 15rem !important;
  }

  .mb-xl-auto {
    margin-bottom: auto !important;
  }

  .ms-xl-0 {
    margin-left: 0 !important;
  }

  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }

  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }

  .ms-xl-3 {
    margin-left: 1rem !important;
  }

  .ms-xl-4 {
    margin-left: 1.8rem !important;
  }

  .ms-xl-5 {
    margin-left: 3rem !important;
  }

  .ms-xl-6 {
    margin-left: 4rem !important;
  }

  .ms-xl-7 {
    margin-left: 5rem !important;
  }

  .ms-xl-8 {
    margin-left: 7.5rem !important;
  }

  .ms-xl-9 {
    margin-left: 10rem !important;
  }

  .ms-xl-10 {
    margin-left: 12.5rem !important;
  }

  .ms-xl-11 {
    margin-left: 15rem !important;
  }

  .ms-xl-auto {
    margin-left: auto !important;
  }

  .m-xl-n1 {
    margin: -0.25rem !important;
  }

  .m-xl-n2 {
    margin: -0.5rem !important;
  }

  .m-xl-n3 {
    margin: -1rem !important;
  }

  .m-xl-n4 {
    margin: -1.8rem !important;
  }

  .m-xl-n5 {
    margin: -3rem !important;
  }

  .m-xl-n6 {
    margin: -4rem !important;
  }

  .m-xl-n7 {
    margin: -5rem !important;
  }

  .m-xl-n8 {
    margin: -7.5rem !important;
  }

  .m-xl-n9 {
    margin: -10rem !important;
  }

  .m-xl-n10 {
    margin: -12.5rem !important;
  }

  .m-xl-n11 {
    margin: -15rem !important;
  }

  .mx-xl-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }

  .mx-xl-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }

  .mx-xl-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }

  .mx-xl-n4 {
    margin-right: -1.8rem !important;
    margin-left: -1.8rem !important;
  }

  .mx-xl-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }

  .mx-xl-n6 {
    margin-right: -4rem !important;
    margin-left: -4rem !important;
  }

  .mx-xl-n7 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-xl-n8 {
    margin-right: -7.5rem !important;
    margin-left: -7.5rem !important;
  }

  .mx-xl-n9 {
    margin-right: -10rem !important;
    margin-left: -10rem !important;
  }

  .mx-xl-n10 {
    margin-right: -12.5rem !important;
    margin-left: -12.5rem !important;
  }

  .mx-xl-n11 {
    margin-right: -15rem !important;
    margin-left: -15rem !important;
  }

  .my-xl-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }

  .my-xl-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }

  .my-xl-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }

  .my-xl-n4 {
    margin-top: -1.8rem !important;
    margin-bottom: -1.8rem !important;
  }

  .my-xl-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }

  .my-xl-n6 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }

  .my-xl-n7 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-xl-n8 {
    margin-top: -7.5rem !important;
    margin-bottom: -7.5rem !important;
  }

  .my-xl-n9 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }

  .my-xl-n10 {
    margin-top: -12.5rem !important;
    margin-bottom: -12.5rem !important;
  }

  .my-xl-n11 {
    margin-top: -15rem !important;
    margin-bottom: -15rem !important;
  }

  .mt-xl-n1 {
    margin-top: -0.25rem !important;
  }

  .mt-xl-n2 {
    margin-top: -0.5rem !important;
  }

  .mt-xl-n3 {
    margin-top: -1rem !important;
  }

  .mt-xl-n4 {
    margin-top: -1.8rem !important;
  }

  .mt-xl-n5 {
    margin-top: -3rem !important;
  }

  .mt-xl-n6 {
    margin-top: -4rem !important;
  }

  .mt-xl-n7 {
    margin-top: -5rem !important;
  }

  .mt-xl-n8 {
    margin-top: -7.5rem !important;
  }

  .mt-xl-n9 {
    margin-top: -10rem !important;
  }

  .mt-xl-n10 {
    margin-top: -12.5rem !important;
  }

  .mt-xl-n11 {
    margin-top: -15rem !important;
  }

  .me-xl-n1 {
    margin-right: -0.25rem !important;
  }

  .me-xl-n2 {
    margin-right: -0.5rem !important;
  }

  .me-xl-n3 {
    margin-right: -1rem !important;
  }

  .me-xl-n4 {
    margin-right: -1.8rem !important;
  }

  .me-xl-n5 {
    margin-right: -3rem !important;
  }

  .me-xl-n6 {
    margin-right: -4rem !important;
  }

  .me-xl-n7 {
    margin-right: -5rem !important;
  }

  .me-xl-n8 {
    margin-right: -7.5rem !important;
  }

  .me-xl-n9 {
    margin-right: -10rem !important;
  }

  .me-xl-n10 {
    margin-right: -12.5rem !important;
  }

  .me-xl-n11 {
    margin-right: -15rem !important;
  }

  .mb-xl-n1 {
    margin-bottom: -0.25rem !important;
  }

  .mb-xl-n2 {
    margin-bottom: -0.5rem !important;
  }

  .mb-xl-n3 {
    margin-bottom: -1rem !important;
  }

  .mb-xl-n4 {
    margin-bottom: -1.8rem !important;
  }

  .mb-xl-n5 {
    margin-bottom: -3rem !important;
  }

  .mb-xl-n6 {
    margin-bottom: -4rem !important;
  }

  .mb-xl-n7 {
    margin-bottom: -5rem !important;
  }

  .mb-xl-n8 {
    margin-bottom: -7.5rem !important;
  }

  .mb-xl-n9 {
    margin-bottom: -10rem !important;
  }

  .mb-xl-n10 {
    margin-bottom: -12.5rem !important;
  }

  .mb-xl-n11 {
    margin-bottom: -15rem !important;
  }

  .ms-xl-n1 {
    margin-left: -0.25rem !important;
  }

  .ms-xl-n2 {
    margin-left: -0.5rem !important;
  }

  .ms-xl-n3 {
    margin-left: -1rem !important;
  }

  .ms-xl-n4 {
    margin-left: -1.8rem !important;
  }

  .ms-xl-n5 {
    margin-left: -3rem !important;
  }

  .ms-xl-n6 {
    margin-left: -4rem !important;
  }

  .ms-xl-n7 {
    margin-left: -5rem !important;
  }

  .ms-xl-n8 {
    margin-left: -7.5rem !important;
  }

  .ms-xl-n9 {
    margin-left: -10rem !important;
  }

  .ms-xl-n10 {
    margin-left: -12.5rem !important;
  }

  .ms-xl-n11 {
    margin-left: -15rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .p-xl-1 {
    padding: 0.25rem !important;
  }

  .p-xl-2 {
    padding: 0.5rem !important;
  }

  .p-xl-3 {
    padding: 1rem !important;
  }

  .p-xl-4 {
    padding: 1.8rem !important;
  }

  .p-xl-5 {
    padding: 3rem !important;
  }

  .p-xl-6 {
    padding: 4rem !important;
  }

  .p-xl-7 {
    padding: 5rem !important;
  }

  .p-xl-8 {
    padding: 7.5rem !important;
  }

  .p-xl-9 {
    padding: 10rem !important;
  }

  .p-xl-10 {
    padding: 12.5rem !important;
  }

  .p-xl-11 {
    padding: 15rem !important;
  }

  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-xl-4 {
    padding-right: 1.8rem !important;
    padding-left: 1.8rem !important;
  }

  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .px-xl-6 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }

  .px-xl-7 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-xl-8 {
    padding-right: 7.5rem !important;
    padding-left: 7.5rem !important;
  }

  .px-xl-9 {
    padding-right: 10rem !important;
    padding-left: 10rem !important;
  }

  .px-xl-10 {
    padding-right: 12.5rem !important;
    padding-left: 12.5rem !important;
  }

  .px-xl-11 {
    padding-right: 15rem !important;
    padding-left: 15rem !important;
  }

  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-xl-4 {
    padding-top: 1.8rem !important;
    padding-bottom: 1.8rem !important;
  }

  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-xl-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-xl-7 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-xl-8 {
    padding-top: 7.5rem !important;
    padding-bottom: 7.5rem !important;
  }

  .py-xl-9 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }

  .py-xl-10 {
    padding-top: 12.5rem !important;
    padding-bottom: 12.5rem !important;
  }

  .py-xl-11 {
    padding-top: 15rem !important;
    padding-bottom: 15rem !important;
  }

  .pt-xl-0 {
    padding-top: 0 !important;
  }

  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }

  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }

  .pt-xl-3 {
    padding-top: 1rem !important;
  }

  .pt-xl-4 {
    padding-top: 1.8rem !important;
  }

  .pt-xl-5 {
    padding-top: 3rem !important;
  }

  .pt-xl-6 {
    padding-top: 4rem !important;
  }

  .pt-xl-7 {
    padding-top: 5rem !important;
  }

  .pt-xl-8 {
    padding-top: 7.5rem !important;
  }

  .pt-xl-9 {
    padding-top: 10rem !important;
  }

  .pt-xl-10 {
    padding-top: 12.5rem !important;
  }

  .pt-xl-11 {
    padding-top: 15rem !important;
  }

  .pe-xl-0 {
    padding-right: 0 !important;
  }

  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }

  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }

  .pe-xl-3 {
    padding-right: 1rem !important;
  }

  .pe-xl-4 {
    padding-right: 1.8rem !important;
  }

  .pe-xl-5 {
    padding-right: 3rem !important;
  }

  .pe-xl-6 {
    padding-right: 4rem !important;
  }

  .pe-xl-7 {
    padding-right: 5rem !important;
  }

  .pe-xl-8 {
    padding-right: 7.5rem !important;
  }

  .pe-xl-9 {
    padding-right: 10rem !important;
  }

  .pe-xl-10 {
    padding-right: 12.5rem !important;
  }

  .pe-xl-11 {
    padding-right: 15rem !important;
  }

  .pb-xl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }

  .pb-xl-4 {
    padding-bottom: 1.8rem !important;
  }

  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }

  .pb-xl-6 {
    padding-bottom: 4rem !important;
  }

  .pb-xl-7 {
    padding-bottom: 5rem !important;
  }

  .pb-xl-8 {
    padding-bottom: 7.5rem !important;
  }

  .pb-xl-9 {
    padding-bottom: 10rem !important;
  }

  .pb-xl-10 {
    padding-bottom: 12.5rem !important;
  }

  .pb-xl-11 {
    padding-bottom: 15rem !important;
  }

  .ps-xl-0 {
    padding-left: 0 !important;
  }

  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }

  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }

  .ps-xl-3 {
    padding-left: 1rem !important;
  }

  .ps-xl-4 {
    padding-left: 1.8rem !important;
  }

  .ps-xl-5 {
    padding-left: 3rem !important;
  }

  .ps-xl-6 {
    padding-left: 4rem !important;
  }

  .ps-xl-7 {
    padding-left: 5rem !important;
  }

  .ps-xl-8 {
    padding-left: 7.5rem !important;
  }

  .ps-xl-9 {
    padding-left: 10rem !important;
  }

  .ps-xl-10 {
    padding-left: 12.5rem !important;
  }

  .ps-xl-11 {
    padding-left: 15rem !important;
  }

  .fs-xl--2 {
    font-size: 0.5627813555rem !important;
  }

  .fs-xl--1 {
    font-size: 0.75rem !important;
  }

  .fs-xl-0 {
    font-size: 1rem !important;
  }

  .fs-xl-1 {
    font-size: 1.333rem !important;
  }

  .fs-xl-2 {
    font-size: 1.777rem !important;
  }

  .fs-xl-3 {
    font-size: 2.369rem !important;
  }

  .fs-xl-4 {
    font-size: 3.157rem !important;
  }

  .fs-xl-5 {
    font-size: 4.199rem !important;
  }

  .fs-xl-6 {
    font-size: 5.584rem !important;
  }

  .fs-xl-7 {
    font-size: 7.427rem !important;
  }

  .fs-xl-8 {
    font-size: 9.878rem !important;
  }

  .text-xl-start {
    text-align: left !important;
  }

  .text-xl-end {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }

  .rounded-xl-top {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
  }

  .rounded-xl-top-lg {
    border-top-left-radius: 0.7rem !important;
    border-top-right-radius: 0.7rem !important;
  }

  .rounded-xl-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .rounded-xl-end {
    border-top-right-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
  }

  .rounded-xl-end-lg {
    border-top-right-radius: 0.7rem !important;
    border-bottom-right-radius: 0.7rem !important;
  }

  .rounded-xl-end-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .rounded-xl-bottom {
    border-bottom-right-radius: 0.5rem !important;
    border-bottom-left-radius: 0.5rem !important;
  }

  .rounded-xl-bottom-lg {
    border-bottom-right-radius: 0.7rem !important;
    border-bottom-left-radius: 0.7rem !important;
  }

  .rounded-xl-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .rounded-xl-start {
    border-bottom-left-radius: 0.5rem !important;
    border-top-left-radius: 0.5rem !important;
  }

  .rounded-xl-start-lg {
    border-bottom-left-radius: 0.7rem !important;
    border-top-left-radius: 0.7rem !important;
  }

  .rounded-xl-start-0 {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }

  .max-vh-xl-25 {
    max-height: 25vh !important;
  }

  .max-vh-xl-50 {
    max-height: 50vh !important;
  }

  .max-vh-xl-75 {
    max-height: 75vh !important;
  }

  .max-vh-xl-100 {
    max-height: 100vh !important;
  }

  .border-xl-x {
    border-left: 1px solid var(--gohub-border-color) !important;
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
  }

  .border-xl-y {
    border-top: 1px solid var(--gohub-border-color) !important;
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-xl-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  .border-xl-dashed {
    border: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-top {
    border-top: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-end {
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-start {
    border-left: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-bottom {
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-x {
    border-left: 1px dashed var(--gohub-border-color) !important;
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xl-dashed-y {
    border-top: 1px dashed var(--gohub-border-color) !important;
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .rounded-xl-0 {
    border-radius: 0 !important;
  }
}
@media (min-width: 1440px) {
  .float-xxl-start {
    float: left !important;
  }

  .float-xxl-end {
    float: right !important;
  }

  .float-xxl-none {
    float: none !important;
  }

  .opacity-xxl-0 {
    opacity: 0 !important;
  }

  .opacity-xxl-25 {
    opacity: 0.25 !important;
  }

  .opacity-xxl-50 {
    opacity: 0.5 !important;
  }

  .opacity-xxl-75 {
    opacity: 0.75 !important;
  }

  .opacity-xxl-85 {
    opacity: 0.85 !important;
  }

  .opacity-xxl-100 {
    opacity: 1 !important;
  }

  .d-xxl-inline {
    display: inline !important;
  }

  .d-xxl-inline-block {
    display: inline-block !important;
  }

  .d-xxl-block {
    display: block !important;
  }

  .d-xxl-grid {
    display: grid !important;
  }

  .d-xxl-table {
    display: table !important;
  }

  .d-xxl-table-row {
    display: table-row !important;
  }

  .d-xxl-table-cell {
    display: table-cell !important;
  }

  .d-xxl-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-xxl-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-xxl-none {
    display: none !important;
  }

  .position-xxl-static {
    position: static !important;
  }

  .position-xxl-absolute {
    position: absolute !important;
  }

  .position-xxl-relative {
    position: relative !important;
  }

  .position-xxl-fixed {
    position: fixed !important;
  }

  .position-xxl-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
  }

  .translate-xxl-middle {
    -webkit-transform: translateX(-50%) translateY(-50%) !important;
    -ms-transform: translateX(-50%) translateY(-50%) !important;
    transform: translateX(-50%) translateY(-50%) !important;
  }

  .translate-xxl-middle-x {
    -webkit-transform: translateX(-50%) !important;
    -ms-transform: translateX(-50%) !important;
    transform: translateX(-50%) !important;
  }

  .translate-xxl-middle-y {
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important;
  }

  .border-xxl {
    border: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-0 {
    border: 0 !important;
  }

  .border-xxl-top {
    border-top: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-top-0 {
    border-top: 0 !important;
  }

  .border-xxl-end {
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-end-0 {
    border-right: 0 !important;
  }

  .border-xxl-bottom {
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-bottom-0 {
    border-bottom: 0 !important;
  }

  .border-xxl-start {
    border-left: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-start-0 {
    border-left: 0 !important;
  }

  .border-xxl-facebook {
    border-color: #3c5a99 !important;
  }

  .border-xxl-google-plus {
    border-color: #dd4b39 !important;
  }

  .border-xxl-twitter {
    border-color: #1da1f2 !important;
  }

  .border-xxl-linkedin {
    border-color: #0077b5 !important;
  }

  .border-xxl-youtube {
    border-color: #ff0001 !important;
  }

  .border-xxl-github {
    border-color: #333333 !important;
  }

  .border-xxl-black {
    border-color: #000 !important;
  }

  .border-xxl-100 {
    border-color: #F5F2FC !important;
  }

  .border-xxl-200 {
    border-color: #f2f2f2 !important;
  }

  .border-xxl-300 {
    border-color: #E7E4EE !important;
  }

  .border-xxl-400 {
    border-color: #bebebe !important;
  }

  .border-xxl-500 {
    border-color: #949494 !important;
  }

  .border-xxl-600 {
    border-color: #7F7F7F !important;
  }

  .border-xxl-700 {
    border-color: #717075 !important;
  }

  .border-xxl-800 {
    border-color: #5E5D61 !important;
  }

  .border-xxl-900 {
    border-color: #403F42 !important;
  }

  .border-xxl-1000 {
    border-color: #212240 !important;
  }

  .border-xxl-1100 {
    border-color: #1c1c1c !important;
  }

  .border-xxl-white {
    border-color: #fff !important;
  }

  .border-xxl-primary {
    border-color: #6f42c1 !important;
  }

  .border-xxl-secondary {
    border-color: #D032D0 !important;
  }

  .border-xxl-success {
    border-color: #7ed321 !important;
  }

  .border-xxl-info {
    border-color: #1C16AF !important;
  }

  .border-xxl-warning {
    border-color: #f37f29 !important;
  }

  .border-xxl-danger {
    border-color: #d0021b !important;
  }

  .border-xxl-light {
    border-color: #F5F2FC !important;
  }

  .border-xxl-dark {
    border-color: #1c1c1c !important;
  }

  .w-xxl-25 {
    width: 25% !important;
  }

  .w-xxl-50 {
    width: 50% !important;
  }

  .w-xxl-75 {
    width: 75% !important;
  }

  .w-xxl-100 {
    width: 100% !important;
  }

  .w-xxl-auto {
    width: auto !important;
  }

  .vw-xxl-25 {
    width: 25vw !important;
  }

  .vw-xxl-50 {
    width: 50vw !important;
  }

  .vw-xxl-75 {
    width: 75vw !important;
  }

  .vw-xxl-100 {
    width: 100vw !important;
  }

  .h-xxl-25 {
    height: 25% !important;
  }

  .h-xxl-50 {
    height: 50% !important;
  }

  .h-xxl-75 {
    height: 75% !important;
  }

  .h-xxl-100 {
    height: 100% !important;
  }

  .h-xxl-auto {
    height: auto !important;
  }

  .vh-xxl-25 {
    height: 25vh !important;
  }

  .vh-xxl-50 {
    height: 50vh !important;
  }

  .vh-xxl-75 {
    height: 75vh !important;
  }

  .vh-xxl-100 {
    height: 100vh !important;
  }

  .min-vh-xxl-25 {
    min-height: 25vh !important;
  }

  .min-vh-xxl-50 {
    min-height: 50vh !important;
  }

  .min-vh-xxl-75 {
    min-height: 75vh !important;
  }

  .min-vh-xxl-100 {
    min-height: 100vh !important;
  }

  .flex-xxl-fill {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-xxl-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-xxl-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-xxl-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-xxl-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-xxl-grow-0 {
    -webkit-box-flex: 0 !important;
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-xxl-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-xxl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-xxl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .flex-xxl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-xxl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-xxl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .gap-xxl-0 {
    gap: 0 !important;
  }

  .gap-xxl-1 {
    gap: 0.25rem !important;
  }

  .gap-xxl-2 {
    gap: 0.5rem !important;
  }

  .gap-xxl-3 {
    gap: 1rem !important;
  }

  .gap-xxl-4 {
    gap: 1.8rem !important;
  }

  .gap-xxl-5 {
    gap: 3rem !important;
  }

  .gap-xxl-6 {
    gap: 4rem !important;
  }

  .gap-xxl-7 {
    gap: 5rem !important;
  }

  .gap-xxl-8 {
    gap: 7.5rem !important;
  }

  .gap-xxl-9 {
    gap: 10rem !important;
  }

  .gap-xxl-10 {
    gap: 12.5rem !important;
  }

  .gap-xxl-11 {
    gap: 15rem !important;
  }

  .justify-content-xxl-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-xxl-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-xxl-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-xxl-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-xxl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .justify-content-xxl-evenly {
    -webkit-box-pack: space-evenly !important;
    -ms-flex-pack: space-evenly !important;
    justify-content: space-evenly !important;
  }

  .align-items-xxl-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-xxl-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-xxl-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-xxl-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-xxl-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-xxl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-xxl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-xxl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-xxl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-xxl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-xxl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-xxl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-xxl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-xxl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-xxl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-xxl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-xxl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }

  .order-xxl-first {
    -webkit-box-ordinal-group: 0 !important;
    -ms-flex-order: -1 !important;
    order: -1 !important;
  }

  .order-xxl-0 {
    -webkit-box-ordinal-group: 1 !important;
    -ms-flex-order: 0 !important;
    order: 0 !important;
  }

  .order-xxl-1 {
    -webkit-box-ordinal-group: 2 !important;
    -ms-flex-order: 1 !important;
    order: 1 !important;
  }

  .order-xxl-2 {
    -webkit-box-ordinal-group: 3 !important;
    -ms-flex-order: 2 !important;
    order: 2 !important;
  }

  .order-xxl-3 {
    -webkit-box-ordinal-group: 4 !important;
    -ms-flex-order: 3 !important;
    order: 3 !important;
  }

  .order-xxl-4 {
    -webkit-box-ordinal-group: 5 !important;
    -ms-flex-order: 4 !important;
    order: 4 !important;
  }

  .order-xxl-5 {
    -webkit-box-ordinal-group: 6 !important;
    -ms-flex-order: 5 !important;
    order: 5 !important;
  }

  .order-xxl-last {
    -webkit-box-ordinal-group: 7 !important;
    -ms-flex-order: 6 !important;
    order: 6 !important;
  }

  .m-xxl-0 {
    margin: 0 !important;
  }

  .m-xxl-1 {
    margin: 0.25rem !important;
  }

  .m-xxl-2 {
    margin: 0.5rem !important;
  }

  .m-xxl-3 {
    margin: 1rem !important;
  }

  .m-xxl-4 {
    margin: 1.8rem !important;
  }

  .m-xxl-5 {
    margin: 3rem !important;
  }

  .m-xxl-6 {
    margin: 4rem !important;
  }

  .m-xxl-7 {
    margin: 5rem !important;
  }

  .m-xxl-8 {
    margin: 7.5rem !important;
  }

  .m-xxl-9 {
    margin: 10rem !important;
  }

  .m-xxl-10 {
    margin: 12.5rem !important;
  }

  .m-xxl-11 {
    margin: 15rem !important;
  }

  .m-xxl-auto {
    margin: auto !important;
  }

  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-xxl-4 {
    margin-right: 1.8rem !important;
    margin-left: 1.8rem !important;
  }

  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-xxl-6 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }

  .mx-xxl-7 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }

  .mx-xxl-8 {
    margin-right: 7.5rem !important;
    margin-left: 7.5rem !important;
  }

  .mx-xxl-9 {
    margin-right: 10rem !important;
    margin-left: 10rem !important;
  }

  .mx-xxl-10 {
    margin-right: 12.5rem !important;
    margin-left: 12.5rem !important;
  }

  .mx-xxl-11 {
    margin-right: 15rem !important;
    margin-left: 15rem !important;
  }

  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-xxl-4 {
    margin-top: 1.8rem !important;
    margin-bottom: 1.8rem !important;
  }

  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-xxl-6 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }

  .my-xxl-7 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }

  .my-xxl-8 {
    margin-top: 7.5rem !important;
    margin-bottom: 7.5rem !important;
  }

  .my-xxl-9 {
    margin-top: 10rem !important;
    margin-bottom: 10rem !important;
  }

  .my-xxl-10 {
    margin-top: 12.5rem !important;
    margin-bottom: 12.5rem !important;
  }

  .my-xxl-11 {
    margin-top: 15rem !important;
    margin-bottom: 15rem !important;
  }

  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xxl-0 {
    margin-top: 0 !important;
  }

  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }

  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }

  .mt-xxl-3 {
    margin-top: 1rem !important;
  }

  .mt-xxl-4 {
    margin-top: 1.8rem !important;
  }

  .mt-xxl-5 {
    margin-top: 3rem !important;
  }

  .mt-xxl-6 {
    margin-top: 4rem !important;
  }

  .mt-xxl-7 {
    margin-top: 5rem !important;
  }

  .mt-xxl-8 {
    margin-top: 7.5rem !important;
  }

  .mt-xxl-9 {
    margin-top: 10rem !important;
  }

  .mt-xxl-10 {
    margin-top: 12.5rem !important;
  }

  .mt-xxl-11 {
    margin-top: 15rem !important;
  }

  .mt-xxl-auto {
    margin-top: auto !important;
  }

  .me-xxl-0 {
    margin-right: 0 !important;
  }

  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }

  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }

  .me-xxl-3 {
    margin-right: 1rem !important;
  }

  .me-xxl-4 {
    margin-right: 1.8rem !important;
  }

  .me-xxl-5 {
    margin-right: 3rem !important;
  }

  .me-xxl-6 {
    margin-right: 4rem !important;
  }

  .me-xxl-7 {
    margin-right: 5rem !important;
  }

  .me-xxl-8 {
    margin-right: 7.5rem !important;
  }

  .me-xxl-9 {
    margin-right: 10rem !important;
  }

  .me-xxl-10 {
    margin-right: 12.5rem !important;
  }

  .me-xxl-11 {
    margin-right: 15rem !important;
  }

  .me-xxl-auto {
    margin-right: auto !important;
  }

  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }

  .mb-xxl-4 {
    margin-bottom: 1.8rem !important;
  }

  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }

  .mb-xxl-6 {
    margin-bottom: 4rem !important;
  }

  .mb-xxl-7 {
    margin-bottom: 5rem !important;
  }

  .mb-xxl-8 {
    margin-bottom: 7.5rem !important;
  }

  .mb-xxl-9 {
    margin-bottom: 10rem !important;
  }

  .mb-xxl-10 {
    margin-bottom: 12.5rem !important;
  }

  .mb-xxl-11 {
    margin-bottom: 15rem !important;
  }

  .mb-xxl-auto {
    margin-bottom: auto !important;
  }

  .ms-xxl-0 {
    margin-left: 0 !important;
  }

  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }

  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }

  .ms-xxl-3 {
    margin-left: 1rem !important;
  }

  .ms-xxl-4 {
    margin-left: 1.8rem !important;
  }

  .ms-xxl-5 {
    margin-left: 3rem !important;
  }

  .ms-xxl-6 {
    margin-left: 4rem !important;
  }

  .ms-xxl-7 {
    margin-left: 5rem !important;
  }

  .ms-xxl-8 {
    margin-left: 7.5rem !important;
  }

  .ms-xxl-9 {
    margin-left: 10rem !important;
  }

  .ms-xxl-10 {
    margin-left: 12.5rem !important;
  }

  .ms-xxl-11 {
    margin-left: 15rem !important;
  }

  .ms-xxl-auto {
    margin-left: auto !important;
  }

  .m-xxl-n1 {
    margin: -0.25rem !important;
  }

  .m-xxl-n2 {
    margin: -0.5rem !important;
  }

  .m-xxl-n3 {
    margin: -1rem !important;
  }

  .m-xxl-n4 {
    margin: -1.8rem !important;
  }

  .m-xxl-n5 {
    margin: -3rem !important;
  }

  .m-xxl-n6 {
    margin: -4rem !important;
  }

  .m-xxl-n7 {
    margin: -5rem !important;
  }

  .m-xxl-n8 {
    margin: -7.5rem !important;
  }

  .m-xxl-n9 {
    margin: -10rem !important;
  }

  .m-xxl-n10 {
    margin: -12.5rem !important;
  }

  .m-xxl-n11 {
    margin: -15rem !important;
  }

  .mx-xxl-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }

  .mx-xxl-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }

  .mx-xxl-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }

  .mx-xxl-n4 {
    margin-right: -1.8rem !important;
    margin-left: -1.8rem !important;
  }

  .mx-xxl-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }

  .mx-xxl-n6 {
    margin-right: -4rem !important;
    margin-left: -4rem !important;
  }

  .mx-xxl-n7 {
    margin-right: -5rem !important;
    margin-left: -5rem !important;
  }

  .mx-xxl-n8 {
    margin-right: -7.5rem !important;
    margin-left: -7.5rem !important;
  }

  .mx-xxl-n9 {
    margin-right: -10rem !important;
    margin-left: -10rem !important;
  }

  .mx-xxl-n10 {
    margin-right: -12.5rem !important;
    margin-left: -12.5rem !important;
  }

  .mx-xxl-n11 {
    margin-right: -15rem !important;
    margin-left: -15rem !important;
  }

  .my-xxl-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }

  .my-xxl-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }

  .my-xxl-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }

  .my-xxl-n4 {
    margin-top: -1.8rem !important;
    margin-bottom: -1.8rem !important;
  }

  .my-xxl-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }

  .my-xxl-n6 {
    margin-top: -4rem !important;
    margin-bottom: -4rem !important;
  }

  .my-xxl-n7 {
    margin-top: -5rem !important;
    margin-bottom: -5rem !important;
  }

  .my-xxl-n8 {
    margin-top: -7.5rem !important;
    margin-bottom: -7.5rem !important;
  }

  .my-xxl-n9 {
    margin-top: -10rem !important;
    margin-bottom: -10rem !important;
  }

  .my-xxl-n10 {
    margin-top: -12.5rem !important;
    margin-bottom: -12.5rem !important;
  }

  .my-xxl-n11 {
    margin-top: -15rem !important;
    margin-bottom: -15rem !important;
  }

  .mt-xxl-n1 {
    margin-top: -0.25rem !important;
  }

  .mt-xxl-n2 {
    margin-top: -0.5rem !important;
  }

  .mt-xxl-n3 {
    margin-top: -1rem !important;
  }

  .mt-xxl-n4 {
    margin-top: -1.8rem !important;
  }

  .mt-xxl-n5 {
    margin-top: -3rem !important;
  }

  .mt-xxl-n6 {
    margin-top: -4rem !important;
  }

  .mt-xxl-n7 {
    margin-top: -5rem !important;
  }

  .mt-xxl-n8 {
    margin-top: -7.5rem !important;
  }

  .mt-xxl-n9 {
    margin-top: -10rem !important;
  }

  .mt-xxl-n10 {
    margin-top: -12.5rem !important;
  }

  .mt-xxl-n11 {
    margin-top: -15rem !important;
  }

  .me-xxl-n1 {
    margin-right: -0.25rem !important;
  }

  .me-xxl-n2 {
    margin-right: -0.5rem !important;
  }

  .me-xxl-n3 {
    margin-right: -1rem !important;
  }

  .me-xxl-n4 {
    margin-right: -1.8rem !important;
  }

  .me-xxl-n5 {
    margin-right: -3rem !important;
  }

  .me-xxl-n6 {
    margin-right: -4rem !important;
  }

  .me-xxl-n7 {
    margin-right: -5rem !important;
  }

  .me-xxl-n8 {
    margin-right: -7.5rem !important;
  }

  .me-xxl-n9 {
    margin-right: -10rem !important;
  }

  .me-xxl-n10 {
    margin-right: -12.5rem !important;
  }

  .me-xxl-n11 {
    margin-right: -15rem !important;
  }

  .mb-xxl-n1 {
    margin-bottom: -0.25rem !important;
  }

  .mb-xxl-n2 {
    margin-bottom: -0.5rem !important;
  }

  .mb-xxl-n3 {
    margin-bottom: -1rem !important;
  }

  .mb-xxl-n4 {
    margin-bottom: -1.8rem !important;
  }

  .mb-xxl-n5 {
    margin-bottom: -3rem !important;
  }

  .mb-xxl-n6 {
    margin-bottom: -4rem !important;
  }

  .mb-xxl-n7 {
    margin-bottom: -5rem !important;
  }

  .mb-xxl-n8 {
    margin-bottom: -7.5rem !important;
  }

  .mb-xxl-n9 {
    margin-bottom: -10rem !important;
  }

  .mb-xxl-n10 {
    margin-bottom: -12.5rem !important;
  }

  .mb-xxl-n11 {
    margin-bottom: -15rem !important;
  }

  .ms-xxl-n1 {
    margin-left: -0.25rem !important;
  }

  .ms-xxl-n2 {
    margin-left: -0.5rem !important;
  }

  .ms-xxl-n3 {
    margin-left: -1rem !important;
  }

  .ms-xxl-n4 {
    margin-left: -1.8rem !important;
  }

  .ms-xxl-n5 {
    margin-left: -3rem !important;
  }

  .ms-xxl-n6 {
    margin-left: -4rem !important;
  }

  .ms-xxl-n7 {
    margin-left: -5rem !important;
  }

  .ms-xxl-n8 {
    margin-left: -7.5rem !important;
  }

  .ms-xxl-n9 {
    margin-left: -10rem !important;
  }

  .ms-xxl-n10 {
    margin-left: -12.5rem !important;
  }

  .ms-xxl-n11 {
    margin-left: -15rem !important;
  }

  .p-xxl-0 {
    padding: 0 !important;
  }

  .p-xxl-1 {
    padding: 0.25rem !important;
  }

  .p-xxl-2 {
    padding: 0.5rem !important;
  }

  .p-xxl-3 {
    padding: 1rem !important;
  }

  .p-xxl-4 {
    padding: 1.8rem !important;
  }

  .p-xxl-5 {
    padding: 3rem !important;
  }

  .p-xxl-6 {
    padding: 4rem !important;
  }

  .p-xxl-7 {
    padding: 5rem !important;
  }

  .p-xxl-8 {
    padding: 7.5rem !important;
  }

  .p-xxl-9 {
    padding: 10rem !important;
  }

  .p-xxl-10 {
    padding: 12.5rem !important;
  }

  .p-xxl-11 {
    padding: 15rem !important;
  }

  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-xxl-4 {
    padding-right: 1.8rem !important;
    padding-left: 1.8rem !important;
  }

  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .px-xxl-6 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }

  .px-xxl-7 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }

  .px-xxl-8 {
    padding-right: 7.5rem !important;
    padding-left: 7.5rem !important;
  }

  .px-xxl-9 {
    padding-right: 10rem !important;
    padding-left: 10rem !important;
  }

  .px-xxl-10 {
    padding-right: 12.5rem !important;
    padding-left: 12.5rem !important;
  }

  .px-xxl-11 {
    padding-right: 15rem !important;
    padding-left: 15rem !important;
  }

  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-xxl-4 {
    padding-top: 1.8rem !important;
    padding-bottom: 1.8rem !important;
  }

  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-xxl-6 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-xxl-7 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }

  .py-xxl-8 {
    padding-top: 7.5rem !important;
    padding-bottom: 7.5rem !important;
  }

  .py-xxl-9 {
    padding-top: 10rem !important;
    padding-bottom: 10rem !important;
  }

  .py-xxl-10 {
    padding-top: 12.5rem !important;
    padding-bottom: 12.5rem !important;
  }

  .py-xxl-11 {
    padding-top: 15rem !important;
    padding-bottom: 15rem !important;
  }

  .pt-xxl-0 {
    padding-top: 0 !important;
  }

  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }

  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }

  .pt-xxl-3 {
    padding-top: 1rem !important;
  }

  .pt-xxl-4 {
    padding-top: 1.8rem !important;
  }

  .pt-xxl-5 {
    padding-top: 3rem !important;
  }

  .pt-xxl-6 {
    padding-top: 4rem !important;
  }

  .pt-xxl-7 {
    padding-top: 5rem !important;
  }

  .pt-xxl-8 {
    padding-top: 7.5rem !important;
  }

  .pt-xxl-9 {
    padding-top: 10rem !important;
  }

  .pt-xxl-10 {
    padding-top: 12.5rem !important;
  }

  .pt-xxl-11 {
    padding-top: 15rem !important;
  }

  .pe-xxl-0 {
    padding-right: 0 !important;
  }

  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }

  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }

  .pe-xxl-3 {
    padding-right: 1rem !important;
  }

  .pe-xxl-4 {
    padding-right: 1.8rem !important;
  }

  .pe-xxl-5 {
    padding-right: 3rem !important;
  }

  .pe-xxl-6 {
    padding-right: 4rem !important;
  }

  .pe-xxl-7 {
    padding-right: 5rem !important;
  }

  .pe-xxl-8 {
    padding-right: 7.5rem !important;
  }

  .pe-xxl-9 {
    padding-right: 10rem !important;
  }

  .pe-xxl-10 {
    padding-right: 12.5rem !important;
  }

  .pe-xxl-11 {
    padding-right: 15rem !important;
  }

  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }

  .pb-xxl-4 {
    padding-bottom: 1.8rem !important;
  }

  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }

  .pb-xxl-6 {
    padding-bottom: 4rem !important;
  }

  .pb-xxl-7 {
    padding-bottom: 5rem !important;
  }

  .pb-xxl-8 {
    padding-bottom: 7.5rem !important;
  }

  .pb-xxl-9 {
    padding-bottom: 10rem !important;
  }

  .pb-xxl-10 {
    padding-bottom: 12.5rem !important;
  }

  .pb-xxl-11 {
    padding-bottom: 15rem !important;
  }

  .ps-xxl-0 {
    padding-left: 0 !important;
  }

  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }

  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }

  .ps-xxl-3 {
    padding-left: 1rem !important;
  }

  .ps-xxl-4 {
    padding-left: 1.8rem !important;
  }

  .ps-xxl-5 {
    padding-left: 3rem !important;
  }

  .ps-xxl-6 {
    padding-left: 4rem !important;
  }

  .ps-xxl-7 {
    padding-left: 5rem !important;
  }

  .ps-xxl-8 {
    padding-left: 7.5rem !important;
  }

  .ps-xxl-9 {
    padding-left: 10rem !important;
  }

  .ps-xxl-10 {
    padding-left: 12.5rem !important;
  }

  .ps-xxl-11 {
    padding-left: 15rem !important;
  }

  .fs-xxl--2 {
    font-size: 0.5627813555rem !important;
  }

  .fs-xxl--1 {
    font-size: 0.75rem !important;
  }

  .fs-xxl-0 {
    font-size: 1rem !important;
  }

  .fs-xxl-1 {
    font-size: 1.333rem !important;
  }

  .fs-xxl-2 {
    font-size: 1.777rem !important;
  }

  .fs-xxl-3 {
    font-size: 2.369rem !important;
  }

  .fs-xxl-4 {
    font-size: 3.157rem !important;
  }

  .fs-xxl-5 {
    font-size: 4.199rem !important;
  }

  .fs-xxl-6 {
    font-size: 5.584rem !important;
  }

  .fs-xxl-7 {
    font-size: 7.427rem !important;
  }

  .fs-xxl-8 {
    font-size: 9.878rem !important;
  }

  .text-xxl-start {
    text-align: left !important;
  }

  .text-xxl-end {
    text-align: right !important;
  }

  .text-xxl-center {
    text-align: center !important;
  }

  .rounded-xxl-top {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
  }

  .rounded-xxl-top-lg {
    border-top-left-radius: 0.7rem !important;
    border-top-right-radius: 0.7rem !important;
  }

  .rounded-xxl-top-0 {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .rounded-xxl-end {
    border-top-right-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
  }

  .rounded-xxl-end-lg {
    border-top-right-radius: 0.7rem !important;
    border-bottom-right-radius: 0.7rem !important;
  }

  .rounded-xxl-end-0 {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .rounded-xxl-bottom {
    border-bottom-right-radius: 0.5rem !important;
    border-bottom-left-radius: 0.5rem !important;
  }

  .rounded-xxl-bottom-lg {
    border-bottom-right-radius: 0.7rem !important;
    border-bottom-left-radius: 0.7rem !important;
  }

  .rounded-xxl-bottom-0 {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .rounded-xxl-start {
    border-bottom-left-radius: 0.5rem !important;
    border-top-left-radius: 0.5rem !important;
  }

  .rounded-xxl-start-lg {
    border-bottom-left-radius: 0.7rem !important;
    border-top-left-radius: 0.7rem !important;
  }

  .rounded-xxl-start-0 {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }

  .max-vh-xxl-25 {
    max-height: 25vh !important;
  }

  .max-vh-xxl-50 {
    max-height: 50vh !important;
  }

  .max-vh-xxl-75 {
    max-height: 75vh !important;
  }

  .max-vh-xxl-100 {
    max-height: 100vh !important;
  }

  .border-xxl-x {
    border-left: 1px solid var(--gohub-border-color) !important;
    border-right: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-x-0 {
    border-left: 0 !important;
    border-right: 0 !important;
  }

  .border-xxl-y {
    border-top: 1px solid var(--gohub-border-color) !important;
    border-bottom: 1px solid var(--gohub-border-color) !important;
  }

  .border-xxl-y-0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  .border-xxl-dashed {
    border: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-top {
    border-top: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-end {
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-start {
    border-left: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-bottom {
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-x {
    border-left: 1px dashed var(--gohub-border-color) !important;
    border-right: 1px dashed var(--gohub-border-color) !important;
  }

  .border-xxl-dashed-y {
    border-top: 1px dashed var(--gohub-border-color) !important;
    border-bottom: 1px dashed var(--gohub-border-color) !important;
  }

  .rounded-xxl-0 {
    border-radius: 0 !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-grid {
    display: grid !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-print-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }

  .d-print-none {
    display: none !important;
  }
}
/*-----------------------------------------------
|   Theme Styles
-----------------------------------------------*/
/*-----------------------------------------------
|   Reboot
-----------------------------------------------*/
::-webkit-input-placeholder {
  opacity: 1;
  color: var(--gohub-400);
}
::-moz-placeholder {
  opacity: 1;
  color: var(--gohub-400);
}
:-ms-input-placeholder {
  opacity: 1;
  color: var(--gohub-400);
}
::-ms-input-placeholder {
  opacity: 1;
  color: var(--gohub-400);
}
::placeholder {
  opacity: 1;
  color: var(--gohub-400);
}

html {
  padding: 0;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
}

pre,
code {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

section {
  position: relative;
  padding-top: 4rem;
  padding-bottom: 4rem;
}
@media (min-width: 960px) {
  section {
    padding-top: 7.5rem;
    padding-bottom: 7.5rem;
  }
}

input,
button,
select,
optgroup,
textarea,
label,
.alert,
.badge,
.blockquote-footer,
.btn,
.navbar,
.pagination,
.valid-feedback,
.invalid-feedback {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

pre {
  margin: 0;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

strong {
  font-weight: 600;
}

label {
  margin-bottom: 0.5rem;
}

pre,
kbd,
samp {
  font-size: 0.75rem;
}

code {
  font-size: 87.5%;
}

ol,
ul {
  padding-left: 2.5rem;
}

/*-----------------------------------------------
|   Navigation bar
-----------------------------------------------*/
.navbar {
  font-weight: 700;
  font-size: 0.8rem;
  letter-spacing: 0.01em;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.alert {
  position: relative;
  padding: 1rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.5rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 3rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}

.alert-primary {
  color: var(--gohub-alert-primary-color);
  background-color: var(--gohub-alert-primary-background);
  border-color: var(--gohub-alert-primary-border-color);
}
.alert-primary .alert-link {
  color: var(--gohub-alert-primary-link-color);
}

.alert-secondary {
  color: var(--gohub-alert-secondary-color);
  background-color: var(--gohub-alert-secondary-background);
  border-color: var(--gohub-alert-secondary-border-color);
}
.alert-secondary .alert-link {
  color: var(--gohub-alert-secondary-link-color);
}

.alert-success {
  color: var(--gohub-alert-success-color);
  background-color: var(--gohub-alert-success-background);
  border-color: var(--gohub-alert-success-border-color);
}
.alert-success .alert-link {
  color: var(--gohub-alert-success-link-color);
}

.alert-info {
  color: var(--gohub-alert-info-color);
  background-color: var(--gohub-alert-info-background);
  border-color: var(--gohub-alert-info-border-color);
}
.alert-info .alert-link {
  color: var(--gohub-alert-info-link-color);
}

.alert-warning {
  color: var(--gohub-alert-warning-color);
  background-color: var(--gohub-alert-warning-background);
  border-color: var(--gohub-alert-warning-border-color);
}
.alert-warning .alert-link {
  color: var(--gohub-alert-warning-link-color);
}

.alert-danger {
  color: var(--gohub-alert-danger-color);
  background-color: var(--gohub-alert-danger-background);
  border-color: var(--gohub-alert-danger-border-color);
}
.alert-danger .alert-link {
  color: var(--gohub-alert-danger-link-color);
}

.alert-light {
  color: var(--gohub-alert-light-color);
  background-color: var(--gohub-alert-light-background);
  border-color: var(--gohub-alert-light-border-color);
}
.alert-light .alert-link {
  color: var(--gohub-alert-light-link-color);
}

.alert-dark {
  color: var(--gohub-alert-dark-color);
  background-color: var(--gohub-alert-dark-background);
  border-color: var(--gohub-alert-dark-border-color);
}
.alert-dark .alert-link {
  color: var(--gohub-alert-dark-link-color);
}

/* -------------------------------------------------------------------------- */
/*                                    Badge                                   */
/* -------------------------------------------------------------------------- */
.badge-rotate {
  z-index: 1;
  -webkit-transform: rotate(-45deg) translate(-30%, -50%);
  -ms-transform: rotate(-45deg) translate(-30%, -50%);
  transform: rotate(-45deg) translate(-30%, -50%);
  position: absolute;
  padding: 0.25rem;
  padding-left: 3rem;
  padding-right: 3rem;
  left: -0.4rem;
  top: 2rem;
}

/*-----------------------------------------------
|   Card
-----------------------------------------------*/
.card {
  border-radius: 1rem;
  border: 0;
}

.card-link {
  font-size: 0.75rem;
  font-weight: 600;
}

.card-body:last-child {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/*-----------------------------------------------
|   Card spacer
-----------------------------------------------*/
.ps-card,
.px-card,
.p-card {
  padding-left: 3rem !important;
}

.pe-card,
.px-card,
.p-card {
  padding-right: 3rem !important;
}

.pt-card,
.py-card,
.p-card {
  padding-top: 3rem !important;
}

.pb-card,
.py-card,
.p-card {
  padding-bottom: 3rem !important;
}

.mt-card {
  margin-top: 3rem !important;
}

.mr-card {
  margin-right: 3rem !important;
}

.ms-ncard,
.mx-ncard,
.m-ncard {
  margin-left: -3rem !important;
}

.me-ncard,
.mx-ncard,
.m-ncard {
  margin-right: -3rem !important;
}

.card-span {
  -webkit-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  border: 0;
}
.card-span .card-span-img {
  position: absolute;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
}

.card-img,
.card-img-top {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.card-bg-line {
  background: -o-linear-gradient(312.44deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), -o-linear-gradient(313.98deg, rgba(64, 207, 252, 0.1) 0%, rgba(106, 17, 250, 0.1) 91.62%), #F2F2F2;
  background: linear-gradient(137.56deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), linear-gradient(136.02deg, rgba(64, 207, 252, 0.1) 0%, rgba(106, 17, 250, 0.1) 91.62%), #F2F2F2;
  -webkit-box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(106, 17, 250, 0.11), inset 20px 21px 34px rgba(64, 207, 252, 0.2);
  box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(106, 17, 250, 0.11), inset 20px 21px 34px rgba(64, 207, 252, 0.2);
  border-radius: 24px;
}

.card-bg-coffee {
  background: -o-linear-gradient(312.44deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), -o-linear-gradient(313.98deg, rgba(10, 25, 172, 0.1) 0%, rgba(153, 0, 191, 0.1) 91.62%), #F2F2F2;
  background: linear-gradient(137.56deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), linear-gradient(136.02deg, rgba(10, 25, 172, 0.1) 0%, rgba(153, 0, 191, 0.1) 91.62%), #F2F2F2;
  -webkit-box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(153, 0, 191, 0.11), inset 20px 21px 34px rgba(10, 25, 172, 0.2);
  box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(153, 0, 191, 0.11), inset 20px 21px 34px rgba(10, 25, 172, 0.2);
  border-radius: 24px;
}

.card-bg-download {
  background: -o-linear-gradient(312.44deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), -o-linear-gradient(313.98deg, rgba(255, 55, 187, 0.1) 0%, rgba(116, 30, 255, 0.1) 91.62%), #F2F2F2;
  background: linear-gradient(137.56deg, #FFFFFF 2.14%, rgba(245, 245, 245, 0) 102.34%), linear-gradient(136.02deg, rgba(255, 55, 187, 0.1) 0%, rgba(116, 30, 255, 0.1) 91.62%), #F2F2F2;
  -webkit-box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(116, 30, 255, 0.11), inset 20px 21px 34px rgba(255, 55, 187, 0.2);
  box-shadow: inset 3px 2px 4px rgba(255, 255, 255, 0.7), inset -2px -2px 6px rgba(0, 0, 0, 0.06), inset -5px -6px 24px rgba(116, 30, 255, 0.11), inset 20px 21px 34px rgba(255, 55, 187, 0.2);
  border-radius: 24px;
}

.team {
  position: absolute;
  top: 0;
  left: 0;
}
.team:hover {
  height: 788px;
  width: 571px;
  background: rgba(242, 242, 242, 0.2);
  -webkit-backdrop-filter: blur(124px);
  backdrop-filter: blur(124px);
  border-radius: 64px;
}

.card-blog {
  position: relative;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 1rem;
  border-radius: 1rem;
}
.card-blog .card-img {
  width: 100%;
  height: 100%;
  -webkit-transition: all 0.8s;
  -o-transition: all 0.8s;
  transition: all 0.8s;
  -o-object-fit: cover;
  object-fit: cover;
}
.card-blog:hover .card-img, .card-blog:focus .card-img {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
.card-blog .overlay-gradient {
  background: none;
}
.card-blog .overlay-gradient:hover, .card-blog .overlay-gradient:focus {
  background: -o-linear-gradient(225deg, rgba(6, 26, 85, 0) 0%, rgba(1, 19, 61, 0.9) 65.07%);
  background: linear-gradient(225deg, rgba(6, 26, 85, 0) 0%, rgba(1, 19, 61, 0.9) 65.07%);
}

.services-card-shadow {
  -webkit-box-shadow: var(--gohub-box-shadow) !important;
  box-shadow: var(--gohub-box-shadow) !important;
}

/* -------------------------------------------------------------------------- */
/*                               Browser Fixing                               */
/* -------------------------------------------------------------------------- */
.firefox .dropcap:first-letter {
  margin-top: 0.175em;
}
.firefox .card-notification .scrollbar-overlay {
  min-height: 19rem;
}

/*-----------------------------------------------
|   Button
-----------------------------------------------*/
.btn {
  letter-spacing: 0.1em;
}

.btn-link {
  font-weight: 700;
}

.btn.btn-outline-light.border-2x {
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover, .btn-light, .btn-light:hover {
  color: #1c1c1c;
}

.btn-xs {
  padding: 0.2rem 0.8rem;
}

.btn-success, .btn-info {
  color: #fff;
}

.btn-light {
  color: #000;
}

.btn-rounded-gradient-primary {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(141.64deg, #6A11FA -22.01%, #40CFFC 125.61%);
  background: linear-gradient(308.36deg, #6A11FA -22.01%, #40CFFC 125.61%);
  width: 11.25rem;
  border: 0;
  border-radius: 10rem !important;
}
.btn-rounded-gradient-primary:hover {
  color: #fff;
}

.btn-gradient-primary {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(141.64deg, #6A11FA -22.01%, #40CFFC 125.61%);
  background: linear-gradient(308.36deg, #6A11FA -22.01%, #40CFFC 125.61%);
  border-radius: 9px;
  width: 8.5rem;
  border: 0;
}
.btn-gradient-primary:hover, .btn-gradient-primary:focus {
  color: #fff;
  background: -o-linear-gradient(141.64deg, #6A11FA -20.01%, #40CFFC 100.61%);
  background: linear-gradient(308.36deg, #6A11FA -20.01%, #40CFFC 100.61%);
}
@media (min-width: 540px) {
  .btn-gradient-primary {
    width: 11.25rem;
  }
}

.btn-gradient-warning {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(316.75deg, #F5C20B 1%, #FE521C 120.29%);
  background: linear-gradient(133.25deg, #F5C20B 1%, #FE521C 120.29%);
  border-radius: 9px;
  width: 8.5rem;
  border: 0;
}
.btn-gradient-warning:hover, .btn-gradient-warning:focus {
  color: #fff;
  background: -o-linear-gradient(329.75deg, #F5C20B 1.66%, #FE521C 142.29%);
  background: linear-gradient(120.25deg, #F5C20B 1.66%, #FE521C 142.29%);
}
@media (min-width: 540px) {
  .btn-gradient-warning {
    width: 11.25rem;
  }
}

.btn-boots-primary {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(141.64deg, #6A11FA -22.01%, #40CFFC 125.61%);
  background: linear-gradient(308.36deg, #6A11FA -22.01%, #40CFFC 125.61%);
  border-radius: 9px;
  width: 14rem;
  border: 0;
}
.btn-boots-primary:hover, .btn-boots-primary:focus {
  color: #fff;
  background: -o-linear-gradient(141.64deg, #6A11FA -20.01%, #40CFFC 100.61%);
  background: linear-gradient(308.36deg, #6A11FA -20.01%, #40CFFC 100.61%);
}
@media (min-width: 1140px) {
  .btn-boots-primary {
    width: 25.569rem;
  }
}

.btn-boots-warning {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(316.75deg, #F5C20B 1%, #FE521C 120.29%);
  background: linear-gradient(133.25deg, #F5C20B 1%, #FE521C 120.29%);
  border-radius: 9px;
  width: 14rem;
  border: 0;
}
.btn-boots-warning:hover, .btn-boots-warning:focus {
  color: #fff;
  background: -o-linear-gradient(329.75deg, #F5C20B 1.66%, #FE521C 142.29%);
  background: linear-gradient(120.25deg, #F5C20B 1.66%, #FE521C 142.29%);
}
@media (min-width: 1440px) {
  .btn-boots-warning {
    width: 25.569rem;
  }
}

.btn-boots-purple {
  color: #fff;
  font-weight: bold;
  background: -o-linear-gradient(316.75deg, #741eff, #ff37bb);
  background: linear-gradient(133.25deg, #741eff, #ff37bb);
  border-radius: 9px;
  width: 14rem;
  border: 0;
}
.btn-boots-purple:hover, .btn-boots-purple:focus {
  color: #fff;
  background: -o-linear-gradient(329.75deg, #741eff, #ff37bb);
  background: linear-gradient(120.25deg, #741eff, #ff37bb);
}
@media (min-width: 1140px) {
  .btn-boots-purple {
    width: 25.569rem;
  }
}

.btn-link {
  color: #F5F2FC;
}

.btn-close-boots {
  cursor: pointer;
  position: relative;
  width: 25px;
  height: 25px;
}
.btn-close-boots:after, .btn-close-boots:before {
  position: absolute;
  left: 75%;
  top: 32%;
  content: " ";
  height: 1.5rem;
  width: 3px;
  background-color: #fff;
}
@media (min-width: 960px) {
  .btn-close-boots:after, .btn-close-boots:before {
    left: 7px;
    top: 5px;
  }
}
.btn-close-boots:after {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.btn-close-boots:before {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.btn-close-boots-container {
  position: fixed;
  height: 2.5rem;
  width: 2.5rem;
  background-color: #000;
  border-radius: 50%;
  right: 3.5rem;
  top: 2rem;
  z-index: 1;
}
@media (min-width: 960px) {
  .btn-close-boots-container {
    position: absolute;
    background-color: transparent;
    height: 0;
    width: 0;
    border-radius: 0;
  }
}

/*-----------------------------------------------
|   Icons group
-----------------------------------------------*/
.icon-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.icon-group .icon-item:not(:last-child) {
  margin-right: 0.5rem;
}

.icon-item {
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: var(--gohub-700);
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  height: 2.5rem;
  width: 2.5rem;
  border: 0;
  font-size: 0.75rem;
  -webkit-box-shadow: var(--gohub-box-shadow-sm);
  box-shadow: var(--gohub-box-shadow-sm);
}
.icon-item:hover, .icon-item:focus {
  background-color: var(--gohub-200);
}
.icon-item.icon-item-sm {
  height: 1.875rem;
  width: 1.875rem;
}
.icon-item.icon-item-lg {
  height: 2.75rem;
  width: 2.75rem;
}

.fa-icon-wait {
  opacity: 0;
}
.fontawesome-i2svg-active .fa-icon-wait {
  opacity: 1;
}

/*-----------------------------------------------
|   Documentation link and Prism
-----------------------------------------------*/
:not(pre) > code[class*=language-], pre[class*=language-] {
  background-color: #F5F2FC;
  border: 1px solid #E7E4EE;
  border-radius: 0.5rem;
  font-size: 87.5%;
}

code[class*=language-], pre[class*=language-] {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.components-nav {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 0.85rem;
}

/*-----------------------------------------------
|   Component examples
-----------------------------------------------*/
/*-----------------------------------------------
|   Borders
-----------------------------------------------*/
.component-example {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}
.component-example [class^=border] {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 8rem;
  height: 8rem;
  margin: 0.75rem;
  background-color: #F5F2FC;
}

/*-----------------------------------------------
|   Component examples
-----------------------------------------------*/
/*-----------------------------------------------
|   Borders
-----------------------------------------------*/
.component-example {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}
.component-example [class^=border] {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 8rem;
  height: 8rem;
  margin: 0.75rem;
  background-color: #F5F2FC;
}

/*-----------------------------------------------
|   Utilities
-----------------------------------------------*/
.border-component [class^=border],
.border-component [class^=rounded-] {
  display: inline-block;
  width: 8rem;
  height: 8rem;
  margin: 0.25rem;
}
.border-component [class^=border] {
  background-color: var(--gohub-100);
}
.border-component [class^=rounded-] {
  background-color: var(--gohub-100);
}

#loaders [class^=spinner] {
  margin-right: 0.5rem;
}

/*-----------------------------------------------
|   Hover Box
-----------------------------------------------*/
.hoverbox {
  position: relative;
}
.hoverbox .hoverbox-content {
  position: absolute;
  border-radius: 1rem;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  -o-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
  margin: 0 !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: rgba(255, 255, 255, 0.01);
  -webkit-backdrop-filter: blur(4rem);
  backdrop-filter: blur(4rem);
}
.hoverbox .hoverbox-content a:hover {
  opacity: 0.8;
}
.hoverbox:hover .hover-box-content-initial, .hoverbox:focus .hover-box-content-initial {
  opacity: 0;
  height: 100%;
}
.hoverbox:hover .hoverbox-content, .hoverbox:focus .hoverbox-content {
  opacity: 1;
}
.hoverbox:hover .as-hoverbox-content, .hoverbox:focus .as-hoverbox-content {
  z-index: 1;
}

.hoverbox-content-gradient {
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(var(--gohub-black)));
  background: -o-linear-gradient(transparent, var(--gohub-black));
  background: linear-gradient(transparent, var(--gohub-black));
}

/* Hover */
.hover-top {
  -webkit-transform: translateY(0) translateZ(0);
  transform: translateY(0) translateZ(0);
  -webkit-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.hover-top.hover-top-shadow-lg:hover, .hover-top.hover-top-shadow-lg:focus {
  -webkit-transform: translateY(-0.3125rem) translateZ(0);
  transform: translateY(-0.3125rem) translateZ(0);
}
.hover-top:hover, .hover-top:focus {
  -webkit-transform: translateY(-0.125rem) translateZ(0);
  transform: translateY(-0.125rem) translateZ(0);
  -webkit-box-shadow: 0.5rem 0.5rem 1.5rem rgba(110, 74, 156, 0.1) !important;
  box-shadow: 0.5rem 0.5rem 1.5rem rgba(110, 74, 156, 0.1) !important;
  background-color: #D032D0;
  color: #fff;
}
.hover-top:hover .heading-color, .hover-top:focus .heading-color {
  color: #fff;
}

/*-----------------------------------------------
|   Object fit and Z-index
-----------------------------------------------*/
.fit-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.z-index-1 {
  z-index: 1 !important;
}

.z-index-2 {
  z-index: 2 !important;
}

.z-index--1 {
  z-index: -1 !important;
}

/*-----------------------------------------------
|   Miscellaneous
-----------------------------------------------*/
.hover-text-decoration-none:hover, .hover-text-decoration-none:focus {
  text-decoration: none;
}

.resize-none {
  resize: none;
}

.collapsed .collapse-icon {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.collapse-icon {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

[data-dismiss=dropdown] *,
[data-bs-offset-top] *,
[data-bs-toggle=collapse] *,
[data-bs-toggle=tooltip] *,
[data-bs-toggle=popover] * {
  pointer-events: none;
}

/*-----------------------------------------------
|   Outline
-----------------------------------------------*/
.outline-none {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.outline-none:hover, .outline-none:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/*-----------------------------------------------
|   Vertical Line (used in kanban header)
-----------------------------------------------*/
.vertical-line:after {
  position: absolute;
  content: "";
  height: 75%;
  width: 1px;
  background: var(--gohub-300);
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.vertical-line.vertical-line-400:after {
  background-color: var(--gohub-400);
}

/* -------------------------------------------------------------------------- */
/*                                 Transition                                 */
/* -------------------------------------------------------------------------- */
.transition-base {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.transition-none {
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
}

.fsp-75 {
  font-size: 75%;
}

/* -------------------------------------------------------------------------- */
/*                                    Width                                   */
/* -------------------------------------------------------------------------- */
.min-w-0 {
  min-width: 0;
}

/* -------------------------------------------------------------------------- */
/*                                    Divider                                   */
/* -------------------------------------------------------------------------- */
.divider-content-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  background-color: var(--gohub-card-bg);
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 0.75rem;
  color: var(--gohub-500);
  white-space: nowrap;
}

/* -------------------------------------------------------------------------- */
/*                                    Zanimation                              */
/* -------------------------------------------------------------------------- */
*[data-zanim-trigger] {
  opacity: 0;
}

/* -------------------------------------------------------------------------- */
/*                                  Anchor JS                                 */
/* -------------------------------------------------------------------------- */
.anchorjs-link {
  text-decoration: none !important;
}

/* -------------------------------------------------------------------------- */
/*                                  Glightbox                                 */
/* -------------------------------------------------------------------------- */
.glightbox-open {
  overflow: unset !important;
}

.gscrollbar-fixer {
  margin-right: 0 !important;
}

[data-gallery] {
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

/* -------------------------------------------------------------------------- */
/*                                 Google Map                                 */
/* -------------------------------------------------------------------------- */
.googlemap .gm-style-iw.gm-style-iw-c {
  -webkit-box-shadow: var(--gohub-box-shadow) !important;
  box-shadow: var(--gohub-box-shadow) !important;
  padding: 1rem !important;
}
.googlemap .gm-style-iw.gm-style-iw-c button[title=Close] {
  margin-top: 1rem !important;
  margin-right: 0.5rem !important;
}

html[dir=rtl] .googlemap .gm-style-iw.gm-style-iw-c button[title=Close] {
  left: unset !important;
  right: 0 !important;
}

.theme-slider .swiper-nav {
  margin-top: 0;
  cursor: pointer;
}
.theme-slider .swiper-nav [class*=swiper-] {
  opacity: 0;
  position: absolute;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  -webkit-transition: opacity 0.4s ease-in-out;
  -o-transition: opacity 0.4s ease-in-out;
  transition: opacity 0.4s ease-in-out;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  color: var(--gohub-700);
  background-color: var(--gohub-swiper-nav-bg) !important;
  -webkit-box-shadow: 0.125rem 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
  box-shadow: 0.125rem 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
  z-index: 1;
  margin: 0;
}
.theme-slider .swiper-nav [class*=swiper-]:hover, .theme-slider .swiper-nav [class*=swiper-]:focus {
  background-color: var(--gohub-white);
  color: var(--gohub-700);
  opacity: 1 !important;
}
.theme-slider .swiper-nav .swiper-button-prev {
  left: 0.5rem;
}
.theme-slider .swiper-nav .swiper-button-next {
  right: 0.5rem;
}
.theme-slider:hover .swiper-nav [class*=swiper-], .theme-slider:focus .swiper-nav [class*=swiper-] {
  opacity: 0.5;
}
.theme-slider .swiper-button-prev:after,
.theme-slider .swiper-button-next:after {
  font-family: swiper-icons;
  font-size: 1rem;
  font-weight: 800;
  text-transform: none !important;
  letter-spacing: 0;
  text-transform: none;
  font-variant: initial;
  line-height: 1;
}

.product-slider {
  position: relative;
}
@media (min-width: 960px) {
  .product-slider {
    height: calc(100% - 3.5625rem);
  }
}
@media (min-width: 1140px) {
  .product-slider {
    height: calc(100% - 3.3125rem);
  }
}
@media (min-width: 1440px) {
  .product-slider {
    height: calc(100% - 4.75rem);
  }
}

/*-----------------------------------------------


|   swiper Carousel
-----------------------------------------------*/
@-webkit-keyframes swiperNavAnimate {
  0% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  85% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }
}
@keyframes swiperNavAnimate {
  0% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  85% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }
}
.swiper-theme .swiper-pagination-bullet {
  background: white;
  color: inherit;
  border: none;
  padding: 0 !important;
  font: inherit;
  width: 2.8125rem;
  height: 0.25rem;
  border-radius: 5px;
  margin: 0.5rem !important;
  opacity: 0.35;
  position: relative;
  overflow: hidden;
}
.swiper-theme .swiper-pagination-bullet-active {
  opacity: 1;
}
.swiper-theme .swiper-pagination-bullet::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: white;
  top: 0;
  left: 0;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
  border-radius: 0.1875rem;
}
.swiper-theme .swiper-pagination-bullet-active {
  background-color: rgba(255, 255, 255, 0.35) !important;
}
.swiper-theme .swiper-pagination-bullet.swiper-pagination-bullet-active::after {
  -webkit-animation: swiperNavAnimate 5s;
  animation: swiperNavAnimate 5s;
}

/*-----------------------------------------------
|   Isotope
-----------------------------------------------*/
.sortable .menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  color: #717075;
  text-decoration: none;
  font-weight: 700;
  font-size: 0.75rem;
  letter-spacing: 0.02rem;
}
.sortable .menu .isotope-nav {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  margin-right: 0.25rem;
}
.sortable .menu .isotope-nav:last-child {
  margin-right: 0;
}
.sortable .menu .isotope-nav.active {
  background: -o-linear-gradient(138.25deg, rgba(116, 30, 255, 0.1) -18.72%, rgba(255, 55, 187, 0.1) 120.42%);
  background: linear-gradient(311.75deg, rgba(116, 30, 255, 0.1) -18.72%, rgba(255, 55, 187, 0.1) 120.42%);
  color: #D032D0;
}

[data-bp] {
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

#bp_container {
  z-index: 11000 !important;
}
#bp_container img {
  border-radius: 5px;
}
#bp_container svg {
  height: 3rem;
}

/*-----------------------------------------------
|   Lead paragraph
-----------------------------------------------*/
.lead {
  font-size: 1.333rem;
  font-weight: 400;
}

/*-----------------------------------------------
|   Drop cap
-----------------------------------------------*/
.dropcap::first-letter {
  font-size: 3em;
  float: left;
  line-height: 0.92;
  margin-right: 0.375rem;
}

/*-----------------------------------------------
|   Letter Spacing
-----------------------------------------------*/
.ls {
  letter-spacing: 0.04em;
}

.ls-2 {
  letter-spacing: 0.25em;
}

/*-----------------------------------------------
|   List
-----------------------------------------------*/
.bullet-inside {
  list-style-position: inside;
}

.style-check li {
  position: relative;
  list-style-type: none;
  padding-left: 0.25rem;
}
.style-check li:before {
  content: url(data:image/svg+xml;base64,PHN2ZyBhcmlhLWhpZGRlbj0idHJ1ZSIgZGF0YS1mYS1wcm9jZXNzZWQ9IiIgZGF0YS1wcmVmaXg9ImZhbCIgZGF0YS1pY29uPSJjaGVjayIgcm9sZT0iaW1nIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIiBjbGFzcz0ic3ZnLWlubGluZS0tZmEgZmEtY2hlY2sgZmEtdy0xNCIgc3R5bGU9ImZvbnQtc2l6ZTogNDhweDsiPjxwYXRoIGZpbGw9ImN1cnJlbnRDb2xvciIgZD0iTTQxMy41MDUgOTEuOTUxTDEzMy40OSAzNzEuOTY2bC05OC45OTUtOTguOTk1Yy00LjY4Ni00LjY4Ni0xMi4yODQtNC42ODYtMTYuOTcxIDBMNi4yMTEgMjg0LjI4NGMtNC42ODYgNC42ODYtNC42ODYgMTIuMjg0IDAgMTYuOTcxbDExOC43OTQgMTE4Ljc5NGM0LjY4NiA0LjY4NiAxMi4yODQgNC42ODYgMTYuOTcxIDBsMjk5LjgxMy0yOTkuODEzYzQuNjg2LTQuNjg2IDQuNjg2LTEyLjI4NCAwLTE2Ljk3MWwtMTEuMzE0LTExLjMxNGMtNC42ODYtNC42ODYtMTIuMjg0LTQuNjg2LTE2Ljk3IDB6IiBjbGFzcz0iIj48L3BhdGg+PC9zdmc+);
  padding-right: 0.1875rem;
  position: absolute;
  top: 0.0625rem;
  left: -1rem;
  background-repeat: no-repeat;
  width: 1rem;
}

/*-----------------------------------------------
|   Horizontal rules
-----------------------------------------------*/
.hr-short {
  width: 4.2rem;
  margin: 0 auto;
}

/*-----------------------------------------------
|   Blockquote
-----------------------------------------------*/
.blockquote-content {
  font-style: italic;
  position: relative;
}
.blockquote-content:before {
  position: absolute;
  left: -3rem;
  top: -0.5rem;
  line-height: 1;
  content: "“";
  display: inline-block;
  color: #E7E4EE;
  font-size: 5.584rem;
}

.blockquote-footer::before {
  -webkit-transform: translateY(0.125rem);
  -ms-transform: translateY(0.125rem);
  transform: translateY(0.125rem);
  font-weight: 400;
  display: inline-block;
}

.blockquote-footer {
  font-weight: 700;
}

/* -------------------------------------------------------------------------- */
/*                                    Text                                    */
/* -------------------------------------------------------------------------- */
.text-smallcaps {
  font-variant: small-caps;
}

.text-superscript {
  vertical-align: super;
}

.text-word-break {
  word-break: break-word;
}

/*-----------------------------------------------
|   Font family
-----------------------------------------------*/
.font-sans-serif {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.font-base {
  font-family: "Baloo Bhaijaan 2", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/*-----------------------------------------------
|   Error Page
-----------------------------------------------*/
.fs-error {
  font-size: 7rem;
}
@media (min-width: 540px) {
  .fs-error {
    font-size: 10rem;
  }
}

/*-----------------------------------------------
|   Text alignment
-----------------------------------------------*/
.text-justify {
  text-align: justify !important;
}

@media (min-width: 960px) {
  .text-vertical {
    -webkit-writing-mode: vertical-rl;
    -ms-writing-mode: tb-rl;
    writing-mode: vertical-rl;
    -webkit-text-orientation: mixed;
    text-orientation: mixed;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    margin: 0;
  }
}
/*-----------------------------------------------
|   Text Gradient Color
-----------------------------------------------*/
.text-gradient-blue {
  background: -webkit-linear-gradient(308.36deg, #8808C4 -22.01%, #4075FC 125.61%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-purple {
  background: -webkit-linear-gradient(100deg, #9900bf, #0a19ac);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-pink-1 {
  background: -webkit-linear-gradient(100deg, #741eff, #ff37bb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-pink-2 {
  background: -webkit-linear-gradient(100deg, #ff6c63, #d2127a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-orange-1 {
  background: -webkit-linear-gradient(100deg, #f5c20b, #fe521c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-orange-2 {
  background: -webkit-linear-gradient(100deg, #e2a226, #ff3326);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-orange-3 {
  background: -webkit-linear-gradient(100deg, #f80000, #ff844f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-gray {
  background: -webkit-linear-gradient(100deg, #403f42, #403f42);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-cyan {
  background: -webkit-linear-gradient(100deg, #017df0, #00f0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/*-----------------------------------------------
| Footer  Gardient Text
-----------------------------------------------*/
.text-gradient-blue-soft {
  background: -webkit-linear-gradient(100deg, rgba(6, 17, 250, 0.8), rgba(64, 207, 252, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-pink-soft {
  background: -webkit-linear-gradient(100deg, #101eff, #ff37bb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-orange-soft-1 {
  background: -webkit-linear-gradient(100deg, #ff7a00, #ff730a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-orange-soft-2 {
  background: -webkit-linear-gradient(100deg, #e2a226, #ff3326);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-green-soft {
  background: -webkit-linear-gradient(100deg, #00b9a3, #00ff5e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-indigo-soft {
  background: -webkit-linear-gradient(100deg, #9900bf, #0a19ac);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-cyan-soft-1 {
  background: -webkit-linear-gradient(100deg, #45f4ff, #ee30ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-cyan-soft-2 {
  background: -webkit-linear-gradient(100deg, #3b7cfa, #00e190);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-danger-soft {
  background: -webkit-linear-gradient(100deg, #ff6c63, #d2127a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/*-----------------------------------------------
|   Backgrounds
-----------------------------------------------*/
.bg-holder {
  position: absolute;
  width: 100%;
  min-height: 100%;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center;
  overflow: hidden;
  will-change: transform, opacity, filter;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background-repeat: no-repeat;
  z-index: 0;
}
.bg-holder.bg-right {
  left: auto;
  right: 0;
}
.bg-holder.overlay:before {
  position: absolute;
  content: "";
  background: rgba(0, 0, 0, 0.25);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.bg-holder.overlay-0:before {
  background: rgba(0, 0, 0, 0.7);
}
.bg-holder.overlay-1:before {
  background: rgba(0, 0, 0, 0.55);
}
.bg-holder.overlay-2:before {
  background: rgba(0, 0, 0, 0.4);
}
.bg-holder.overlay-3:before {
  background: rgba(0, 0, 0, 0.88);
}
.bg-holder.overlay-youtube:before {
  background: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 16, 58, 0.6)), to(rgba(0, 16, 58, 0.8)));
  background: -o-linear-gradient(bottom, rgba(0, 16, 58, 0.6), rgba(0, 16, 58, 0.8));
  background: linear-gradient(0deg, rgba(0, 16, 58, 0.6), rgba(0, 16, 58, 0.8));
}
.bg-holder .bg-video {
  position: absolute;
  display: block;
  z-index: -1;
  top: 0;
  left: 0;
  -o-object-fit: cover;
  object-fit: cover;
  height: 100%;
  min-width: 100%;
}
.bg-holder .bg-youtube {
  position: absolute !important;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.bg-backdrop {
  position: relative;
  background: rgba(242, 242, 242, 0.4);
  -webkit-backdrop-filter: blur(1rem);
  backdrop-filter: blur(1rem);
  border-radius: 16px;
  z-index: 1;
}
.bg-backdrop::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  -webkit-filter: blur(1px);
  filter: blur(1px);
  border-radius: 16px;
  background: rgba(242, 242, 242, 0.4);
  z-index: -1;
}
.firefox .bg-backdrop::before {
  background: #f2f2f2;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-glass {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-card {
  background-size: contain;
  background-position: right;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
[dir=rtl] .bg-card {
  background-position: left;
  -webkit-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  transform: scaleX(-1);
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

/*-----------------------------------------------
|   Background gradient
-----------------------------------------------*/
.bg-line-chart-gradient {
  background-image: var(--gohub-line-chart-gradient);
  background-position: center;
}

.bg-card-gradient {
  background-image: var(--gohub-card-gradient);
  background-position: center;
}

.bg-progress-gradient {
  background-image: var(--gohub-progress-gradient);
  background-position: center;
}

.bg-auth-circle-shape,
.bg-auth-circle-shape-2 {
  display: none;
}
@media (min-width: 960px) {
  .bg-auth-circle-shape,
.bg-auth-circle-shape-2 {
    display: block;
  }
}

.bg-auth-circle-shape {
  position: absolute;
  right: -8.75rem;
  top: -5.125rem;
}

.bg-auth-circle-shape-2 {
  position: absolute;
  left: -6.25rem;
  bottom: -2.4375rem;
}

.bg-auth-card-shape {
  background-position: 0 100%;
}
@media (min-width: 720px) {
  .bg-auth-card-shape {
    background-position: 0 133%;
  }
}

.bg-shape {
  position: relative;
  overflow: hidden;
  background-color: var(--gohub-bg-shape-bg);
}
.bg-shape:after, .bg-shape:before {
  position: absolute;
  content: "";
  border-radius: 50%;
}
.bg-shape:after {
  background-image: var(--gohub-bg-shape-bg-ltd);
}
.bg-shape:before {
  background-image: var(--gohub-bg-shape-bg-dtl);
}

.bg-circle-shape:after {
  height: 15.625rem;
  width: 115%;
  left: 32%;
  top: -188%;
}
.bg-circle-shape:before {
  height: 332%;
  width: 45%;
  left: -9%;
  top: 0.5625rem;
}

.modal-shape-header:before {
  height: 28.9375rem;
  width: 155%;
  right: 23%;
  top: -357%;
}
.modal-shape-header:after {
  height: 289%;
  width: 45%;
  right: -10%;
  top: 2.5rem;
}

.showcase-page-gradient {
  background: -webkit-gradient(linear, left bottom, left top, from(rgba(163, 20, 20, 0.5)), to(rgba(27, 27, 27, 0.5)));
  background: -o-linear-gradient(bottom, rgba(163, 20, 20, 0.5), rgba(27, 27, 27, 0.5));
  background: linear-gradient(0deg, rgba(163, 20, 20, 0.5), rgba(27, 27, 27, 0.5));
  mix-blend-mode: normal;
}

.bg-transparent-50 {
  background-color: var(--gohub-transparent-50);
}

.bg-portfolio-img {
  background: rgba(255, 255, 255, 0.01);
  -webkit-backdrop-filter: blur(64px);
  backdrop-filter: blur(64px);
  border-radius: 16px;
}

/*-----------------------------------------------
| Footer Background
-----------------------------------------------*/
.bg-soft-blue {
  background: -o-linear-gradient(141.64deg, rgba(106, 17, 250, 0.1) -22.01%, rgba(64, 207, 252, 0.1) 125.61%);
  background: linear-gradient(308.36deg, rgba(106, 17, 250, 0.1) -22.01%, rgba(64, 207, 252, 0.1) 125.61%);
}

.bg-soft-pink {
  background: -o-linear-gradient(138.25deg, rgba(116, 30, 255, 0.1) -18.72%, rgba(255, 55, 187, 0.1) 120.42%);
  background: linear-gradient(311.75deg, rgba(116, 30, 255, 0.1) -18.72%, rgba(255, 55, 187, 0.1) 120.42%);
}

.bg-soft-orange-1 {
  background: -o-linear-gradient(140.96deg, rgba(255, 122, 0, 0.1) 0.25%, rgba(250, 255, 10, 0.1) 96%);
  background: linear-gradient(309.04deg, rgba(255, 122, 0, 0.1) 0.25%, rgba(250, 255, 10, 0.1) 96%);
}

.bg-soft-orange-2 {
  background: -o-linear-gradient(141.64deg, rgba(226, 162, 38, 0.1) -22.01%, rgba(255, 51, 38, 0.1) 125.61%);
  background: linear-gradient(308.36deg, rgba(226, 162, 38, 0.1) -22.01%, rgba(255, 51, 38, 0.1) 125.61%);
}

.bg-soft-green {
  background: -o-linear-gradient(322.39deg, rgba(0, 185, 163, 0.1) -6.08%, rgba(0, 255, 94, 0.1) 125.2%);
  background: linear-gradient(127.61deg, rgba(0, 185, 163, 0.1) -6.08%, rgba(0, 255, 94, 0.1) 125.2%);
}

.bg-soft-indigo {
  background: -o-linear-gradient(138.25deg, rgba(153, 0, 191, 0.1) -18.72%, rgba(10, 25, 172, 0.1) 120.42%);
  background: linear-gradient(311.75deg, rgba(153, 0, 191, 0.1) -18.72%, rgba(10, 25, 172, 0.1) 120.42%);
}

.bg-soft-cyan-1 {
  background: -o-linear-gradient(142.12deg, rgba(4, 96, 182, 0.1) -28.4%, rgba(0, 240, 255, 0.1) 128.33%);
  background: linear-gradient(307.88deg, rgba(4, 96, 182, 0.1) -28.4%, rgba(0, 240, 255, 0.1) 128.33%);
}

.bg-soft-cyan-2 {
  background: -o-linear-gradient(138.25deg, rgba(59, 124, 250, 0.1) -18.72%, rgba(59, 124, 250, 0.1) -18.71%, rgba(0, 225, 144, 0.1) 120.42%);
  background: linear-gradient(311.75deg, rgba(59, 124, 250, 0.1) -18.72%, rgba(59, 124, 250, 0.1) -18.71%, rgba(0, 225, 144, 0.1) 120.42%);
}

.bg-soft-danger {
  background: -o-linear-gradient(138.25deg, rgba(205, 12, 0, 0.1) -18.71%, rgba(255, 28, 96, 0.1) 120.42%);
  background: linear-gradient(311.75deg, rgba(205, 12, 0, 0.1) -18.71%, rgba(255, 28, 96, 0.1) 120.42%);
}

/* -------------------------------------------------------------------------- */
/*                                   Borders                                  */
/* -------------------------------------------------------------------------- */
.overflow-hidden[class*=rounded] {
  -webkit-mask-image: radial-gradient(#fff, #000);
  mask-image: radial-gradient(#fff, #000);
}

.border-top-2 {
  border-top-width: 2px !important;
}

.border-end-2 {
  border-right-width: 2px !important;
}

.border-bottom-2 {
  border-bottom-width: 2px !important;
}

.border-start-2 {
  border-left-width: 2px !important;
}

/* -------------------------------------------------------------------------- */
/*                                  Position                                  */
/* -------------------------------------------------------------------------- */
.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
  position: relative;
}

.all-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.absolute-centered {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
}

.absolute-horizontal-centered {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

/* -------------------------------------------------------------------------- */
/*                                    Flex                                    */
/* -------------------------------------------------------------------------- */
.flex-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex-between-center {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-end-center {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-between-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.flex-1 {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

@media (min-width: 960px) {
  .flex-lg-basis-0 {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }
}
.link-primary {
  color: var(--gohub-primary);
}
.link-primary:hover, .link-primary:focus {
  color: var(--gohub-colored-link-primary-hover-color);
}

.link-secondary {
  color: var(--gohub-secondary);
}
.link-secondary:hover, .link-secondary:focus {
  color: var(--gohub-colored-link-secondary-hover-color);
}

.link-success {
  color: var(--gohub-success);
}
.link-success:hover, .link-success:focus {
  color: var(--gohub-colored-link-success-hover-color);
}

.link-info {
  color: var(--gohub-info);
}
.link-info:hover, .link-info:focus {
  color: var(--gohub-colored-link-info-hover-color);
}

.link-warning {
  color: var(--gohub-warning);
}
.link-warning:hover, .link-warning:focus {
  color: var(--gohub-colored-link-warning-hover-color);
}

.link-danger {
  color: var(--gohub-danger);
}
.link-danger:hover, .link-danger:focus {
  color: var(--gohub-colored-link-danger-hover-color);
}

.link-light {
  color: var(--gohub-light);
}
.link-light:hover, .link-light:focus {
  color: var(--gohub-colored-link-light-hover-color);
}

.link-dark {
  color: var(--gohub-dark);
}
.link-dark:hover, .link-dark:focus {
  color: var(--gohub-colored-link-dark-hover-color);
}

.link-black {
  color: var(--gohub-black) !important;
}
.link-black:hover, .link-black:focus {
  color: var(--gohub-colored-link-black-hover-color) !important;
}

.link-100 {
  color: var(--gohub-100) !important;
}
.link-100:hover, .link-100:focus {
  color: var(--gohub-colored-link-100-hover-color) !important;
}

.link-200 {
  color: var(--gohub-200) !important;
}
.link-200:hover, .link-200:focus {
  color: var(--gohub-colored-link-200-hover-color) !important;
}

.link-300 {
  color: var(--gohub-300) !important;
}
.link-300:hover, .link-300:focus {
  color: var(--gohub-colored-link-300-hover-color) !important;
}

.link-400 {
  color: var(--gohub-400) !important;
}
.link-400:hover, .link-400:focus {
  color: var(--gohub-colored-link-400-hover-color) !important;
}

.link-500 {
  color: var(--gohub-500) !important;
}
.link-500:hover, .link-500:focus {
  color: var(--gohub-colored-link-500-hover-color) !important;
}

.link-600 {
  color: var(--gohub-600) !important;
}
.link-600:hover, .link-600:focus {
  color: var(--gohub-colored-link-600-hover-color) !important;
}

.link-700 {
  color: var(--gohub-700) !important;
}
.link-700:hover, .link-700:focus {
  color: var(--gohub-colored-link-700-hover-color) !important;
}

.link-800 {
  color: var(--gohub-800) !important;
}
.link-800:hover, .link-800:focus {
  color: var(--gohub-colored-link-800-hover-color) !important;
}

.link-900 {
  color: var(--gohub-900) !important;
}
.link-900:hover, .link-900:focus {
  color: var(--gohub-colored-link-900-hover-color) !important;
}

.link-1000 {
  color: var(--gohub-1000) !important;
}
.link-1000:hover, .link-1000:focus {
  color: var(--gohub-colored-link-1000-hover-color) !important;
}

.link-1100 {
  color: var(--gohub-1100) !important;
}
.link-1100:hover, .link-1100:focus {
  color: var(--gohub-colored-link-1100-hover-color) !important;
}

.link-white {
  color: var(--gohub-white) !important;
}
.link-white:hover, .link-white:focus {
  color: var(--gohub-colored-link-white-hover-color) !important;
}

/*-----------------------------------------------
|   Dropdown
-----------------------------------------------*/
.dropdown-menu {
  font-size: 0.75rem;
  border-radius: 0.7rem;
}

.dropdown-indicator {
  position: relative;
}
.dropdown-indicator:after {
  content: "";
  display: block;
  position: absolute;
  right: 5px;
  height: 0.4rem;
  width: 0.4rem;
  border-right: 1px solid #717075;
  border-bottom: 1px solid #717075;
  top: 50%;
  -webkit-transform: translateY(-50%) rotate(45deg);
  -ms-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transform-origin: center;
  -ms-transform-origin: center;
  transform-origin: center;
  -webkit-transition-property: border-color, -webkit-transform;
  transition-property: border-color, -webkit-transform;
  -o-transition-property: transform, border-color;
  transition-property: transform, border-color;
  transition-property: transform, border-color, -webkit-transform;
}
.dropdown-indicator[aria-expanded=true]:after {
  -webkit-transform: translateY(-50%) rotate(225deg);
  -ms-transform: translateY(-50%) rotate(225deg);
  transform: translateY(-50%) rotate(225deg);
}

.dropdown-caret-none:after, .dropdown-caret-none:before {
  display: none !important;
}

.dropdown-md {
  min-width: 16.625rem;
}

/*-----------------------------------------------
|   Form
-----------------------------------------------*/
label {
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.02em;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1070;
  display: block;
  max-width: 276px;
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.45;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.75rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.7rem;
  -webkit-box-shadow: var(--gohub-box-shadow);
  box-shadow: var(--gohub-box-shadow);
}
.popover .popover-arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.7rem;
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top, .bs-popover-auto[x-placement^=top] {
  margin-bottom: 0.5rem;
}
.bs-popover-top > .popover-arrow, .bs-popover-auto[x-placement^=top] > .popover-arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[x-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[x-placement^=top] > .popover-arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.bs-popover-end, .bs-popover-auto[x-placement^=right] {
  margin-left: 0.5rem;
}
.bs-popover-end > .popover-arrow, .bs-popover-auto[x-placement^=right] > .popover-arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.7rem 0;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[x-placement^=right] > .popover-arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[x-placement^=right] > .popover-arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.bs-popover-bottom, .bs-popover-auto[x-placement^=bottom] {
  margin-top: 0.5rem;
}
.bs-popover-bottom > .popover-arrow, .bs-popover-auto[x-placement^=bottom] > .popover-arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[x-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[x-placement^=bottom] > .popover-arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f0f0f0;
}

.bs-popover-start, .bs-popover-auto[x-placement^=left] {
  margin-right: 0.5rem;
}
.bs-popover-start > .popover-arrow, .bs-popover-auto[x-placement^=left] > .popover-arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.7rem 0;
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[x-placement^=left] > .popover-arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[x-placement^=left] > .popover-arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: var(--gohub-headings-color);
  background-color: #f0f0f0;
  border-top-left-radius: calc(0.7rem - 1px);
  border-top-right-radius: calc(0.7rem - 1px);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 1rem 1rem;
  color: #717075;
}

/*-----------------------------------------------
|   Font family, Position and Overflow [NTR]
-----------------------------------------------*/
.overflow-hidden, .overflow-hidden-x {
  overflow-x: hidden;
}

.overflow-hidden, .overflow-hidden-y {
  overflow-y: hidden;
}

.yt-video {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -100000;
  pointer-events: none;
}

iframe.yt-video {
  opacity: 0;
}
iframe.yt-video.loaded {
  opacity: 1;
}

.player {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
}
/*# sourceMappingURL=theme.css.map */

/* Custom Red Theme Overrides - Professional Red Color Scheme */
.btn-primary, .btn.btn-primary {
  background-color: #D32F2F !important;
  border-color: #D32F2F !important;
}

.btn-primary:hover, .btn.btn-primary:hover {
  background-color: #B71C1C !important;
  border-color: #B71C1C !important;
}

.btn-secondary, .btn.btn-secondary {
  background-color: #B71C1C !important;
  border-color: #B71C1C !important;
}

.btn-secondary:hover, .btn.btn-secondary:hover {
  background-color: #8D1515 !important;
  border-color: #8D1515 !important;
}

.bg-primary {
  background-color: #D32F2F !important;
}

.bg-secondary {
  background-color: #B71C1C !important;
}

.text-primary {
  color: #D32F2F !important;
}

.text-secondary {
  color: #B71C1C !important;
}

.navbar-brand {
  color: #D32F2F !important;
}

.nav-pills .nav-link.active {
  background-color: #D32F2F !important;
}
