<link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Asap:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />

<script src="{{ asset('assets/front-end/assets/js/loopple/kit.fontawesome.js') }}" crossorigin="anonymous"></script>

<link href="{{ asset('assets/front-end/assets/css/nucleo-icons.css') }}" rel="stylesheet" />
<link href="{{ asset('assets/front-end/assets/css/nucleo-svg.css') }}" rel="stylesheet" />
{{--
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}"> --}}
@if($active_theme === 'old' || (isset($theme) && $theme === 'old'))
    <!-- Old Theme Styles -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/old/css/theme.css') }}">
@else
    <!-- New Theme Styles (Current) -->
    <link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/theme.css') }}?v={{ time() }}">
@endif
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/loopple/loopple.css') }}">
<link rel="stylesheet" href="{{ asset('assets/front-end/assets/css/custom.css') }}">

<!-- Theme Color Overrides - Load Last -->
@if($active_theme !== 'old' && (!isset($theme) || $theme !== 'old'))
<style>
/* Professional Red Theme Overrides - Ensure these load last */
:root {
    --gohub-primary: #D32F2F !important;
    --gohub-secondary: #B71C1C !important;
    --gohub-primary-rgb: 211, 47, 47 !important;
    --gohub-secondary-rgb: 183, 28, 28 !important;
}

.btn-primary, .btn.btn-primary, .btn-btn-primary {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn.btn-primary:hover, .btn-btn-primary:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary, .btn.btn-secondary {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}

.btn-secondary:hover, .btn.btn-secondary:hover {
    background-color: #8D1515 !important;
    border-color: #8D1515 !important;
    color: #ffffff !important;
}

.bg-primary {
    background-color: #D32F2F !important;
}

.bg-secondary {
    background-color: #B71C1C !important;
}

.text-primary {
    color: #D32F2F !important;
}

.text-secondary {
    color: #B71C1C !important;
}

.navbar-brand {
    color: #D32F2F !important;
}

.nav-pills .nav-link.active {
    background-color: #D32F2F !important;
}

/* Theme icon update for modern theme */
.theme-modern .theme-icon {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
}

/* Specific Homepage and Header Button Styling */
/* Get Started button on homepage */
.btn-gradient-dark {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%) !important;
    border: none !important;
    color: #ffffff !important;
}

.btn-gradient-dark:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8D1515 100%) !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3) !important;
}

/* Login button in header - only this button, not the whole header */
.btn-outline-login {
    background-color: transparent !important;
    border: 2px solid #D32F2F !important;
    color: #D32F2F !important;
}

.btn-outline-login:hover {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

/* Ensure header background stays normal (not red) */
.navbar.bg-primary {
    background-color: #ffffff !important;
    background: #ffffff !important;
}

/* Keep navbar brand and other elements normal */
.navbar-brand {
    color: #333333 !important;
}

.nav-link {
    color: #333333 !important;
}

.nav-link:hover {
    color: #D32F2F !important;
}

/* Additional Get Started buttons throughout the site */
.btn-primary.btn-lg {
    background-color: #D32F2F !important;
    border-color: #D32F2F !important;
    color: #ffffff !important;
}

.btn-primary.btn-lg:hover {
    background-color: #B71C1C !important;
    border-color: #B71C1C !important;
    color: #ffffff !important;
}
</style>
@endif
<link href="{{ asset('assets/css/toastr.min.css') }}" rel="stylesheet" />